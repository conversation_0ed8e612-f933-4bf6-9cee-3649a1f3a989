import CommonPage from './common_page'

class SamplePage extends CommonPage {
    url = '/material-samples/'
    
    selectors = {
        sample_sets: 'section.samples-sets',
        cta_add_to_cart: '[data-testid=samples-a2c-cta]',
        sample_price_string: '[data-testid=sample-price-string]',
        modal_view_cart_cta: '[data-testid="view-cart"]',
        modal_go_to_checkout_cta: '[data-testid="checkout"]',
    }

    addToCartSampleWithIndex(index=0) {
        cy.get(this.selectors.sample_sets).find(this.selectors.cta_add_to_cart).eq(index).as('sampleSet')
        cy.get('@sampleSet').scrollIntoView().should('be.visible')
        cy.wait(1000)
        cy.get('@sampleSet').click()
    }

    getSampleSetPriceAsString(index=0) {
        return cy.get(this.selectors.sample_price_string).eq(index).invoke('text')
    }

    acceptModalAndGoToCheckout() {
        cy.get(this.selectors.modal_go_to_checkout_cta).click()
    }

    acceptModalAndGoToCart() {
        cy.get(this.selectors.modal_view_cart_cta, { timeout:10000 }).click()
    }
}

export default new SamplePage()
