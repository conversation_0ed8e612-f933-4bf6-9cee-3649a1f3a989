import hp from "../pages/home_page"
import plp from "../pages/product_listing_page"
import pdp from "../pages/product_page"
import checkout from "../pages/checkout_page"
import sample_page from "../pages/sample_page"
import login_page from "../pages/login_page"
import cart from "../pages/cart_page"
import { isProdEnv, checkIfCartItemsCountEquals } from "./common_functions"

const url = Cypress.config('baseUrl')

function testNotLoggedOnUserBuyingFurniture(region, category, language) {
    it(buildTestName(`as NOT logged on user`, region, category, language), () => {
        cy.visit(buildUrl(region, language))
        verifyLang(language)
        cy.agreeCookies()
        runStandardFlowForFurniture(category)
        pdp.waitForConfiguratorToBeReadyAndAddToCart()
        checkIfCartItemsCountEquals(1)
        pdp.acceptModalAndGoToCart()
        cart.goToCheckoutFromCart()
        login_page.continueAsGuest()
        checkout.fillForm(Cypress.config('orderData'))
        checkout.checkVisibilityOfPaymentMethods()
        if (!isProdEnv()) {
            checkout.payDropin('card')
            verifyConfirmationPage()
        }
    })
}

function testNotLoggedOnUserBuyingFurnitureOnMobile(region, category, language, payment_method) {
    it(buildTestName(`as NOT logged on user`, region, category, language, payment_method), () => {
        cy.visit(buildUrl(region, language))
        verifyLang(language)
        cy.agreeCookies()
        runStandardFlowForFurnitureOnMobile(category)
        pdp.waitForConfiguratorToBeReadyAndAddToCart()
        checkIfCartItemsCountEquals(1)
        pdp.acceptModalAndGoToCart()
        cart.goToCheckoutFromCart()
        login_page.continueAsGuest()
        checkout.fillForm(Cypress.config('orderData'))
        checkout.checkVisibilityOfPaymentMethods()
        if (!isProdEnv()) {
            checkout.payDropin('card')
            verifyConfirmationPage()
        }
    })
}

function testNotLoggedOnUserBuyingSample(region, language) {
    it(buildTestName(`as NOT logged on user`, region, 'material sample', language), () => {
        cy.visit(buildUrl(region, language))
        verifyLang(language)
        cy.agreeCookies()
        hp.openMegaMenu()
        hp.navigateToSamples()
        cy.wait(3000)
        sample_page.addToCartSampleWithIndex(17)
        cy.wait(1000)
        checkIfCartItemsCountEquals(1)
        pdp.acceptModalAndGoToCart()
        cart.goToCheckoutFromCart()
        login_page.continueAsGuest()
        checkout.fillForm(Cypress.config('orderData'))
        checkout.checkVisibilityOfPaymentMethods()
        if (!isProdEnv()) {
            checkout.payDropin('card')
            verifyConfirmationPage()
        }
    })
}

function testNotLoggedOnUserBuyingSampleOnMobile(region, language, payment_method) {
    it(buildTestName(`as NOT logged on user`, region, 'material sample', language, payment_method), () => {
        cy.visit(buildUrl(region, language))
        verifyLang(language)
        cy.agreeCookies()
        hp.openMegaMenuOnMobile()
        hp.navigateToSamplesOnMobile()
        cy.wait(3000)
        sample_page.addToCartSampleWithIndex(17)
        cy.wait(1000)
        checkIfCartItemsCountEquals(1)
        pdp.acceptModalAndGoToCart()
        cart.goToCheckoutFromCart()
        login_page.continueAsGuest()
        checkout.fillForm(Cypress.config('orderData'))
        checkout.checkVisibilityOfPaymentMethods()
        if (!isProdEnv()) {
            checkout.payDropin('card')
            verifyConfirmationPage()
        }
    })
}

function testNotLoggedOnUserBuyingFurnitureAndSample(region, category, language) {
    it(buildTestName(`as NOT logged on user`, region, (`${category} and material sample`), language, ), () => {
        cy.visit(buildUrl(region, language))
        verifyLang(language)
        cy.agreeCookies()
        runStandardFlowForFurniture(category, )
        pdp.waitForConfiguratorToBeReadyAndAddToCart()
        pdp.closeModalAfterA2C()
        hp.openMegaMenu()
        hp.navigateToSamples()
        cy.wait(3000)
        sample_page.addToCartSampleWithIndex(17)
        checkIfCartItemsCountEquals(2)
        pdp.acceptModalAndGoToCart()
        cart.goToCheckoutFromCart()
        login_page.continueAsGuest()
        checkout.fillForm(Cypress.config('orderData'))
        checkout.checkVisibilityOfPaymentMethods()
        if (!isProdEnv()) {
            checkout.payDropin('card')
            verifyConfirmationPage()
        }
    })
}

function testNotLoggedOnUserBuyingFurnitureAndSampleOnMobile(region, category, language, payment_method) {
    it(buildTestName(`as NOT logged on user`, region, (`${category} and material sample`), language, payment_method), () => {
        cy.visit(buildUrl(region, language))
        verifyLang(language)
        cy.agreeCookies()
        runStandardFlowForFurnitureOnMobile(category)
        pdp.waitForConfiguratorToBeReadyAndAddToCart()
        pdp.closeModalAfterA2C()
        hp.openMegaMenuOnMobile()
        hp.navigateToSamplesOnMobile()
        cy.wait(3000)
        sample_page.addToCartSampleWithIndex(17)
        cy.wait(3000)
        checkIfCartItemsCountEquals(2)
        pdp.acceptModalAndGoToCart()
        cart.goToCheckoutFromCart()
        login_page.continueAsGuest()
        checkout.fillForm(Cypress.config('orderData'))
        checkout.checkVisibilityOfPaymentMethods()
        if (!isProdEnv()) {
            checkout.payDropin('card')
            verifyConfirmationPage()
        }
    })
}

function runStandardFlowForFurniture(category) {
    hp.openMegaMenu()
    cy.wait(1000)
    hp.navigateToCategory(category)
    plp.selectProductFromGrid()
}

function runStandardFlowForFurnitureOnMobile(category) {
    cy.scrollTo('top')
    hp.openMegaMenuOnMobile()
    cy.wait(1000)
    hp.navigateToCategoryOnMobile(category)
    plp.selectProductFromGridOnMobile()
}

function buildUrl(region, language) {
    return `${url}/${language}-${region}`
}

function verifyConfirmationPage() {
    cy.url({ timeout:10000 }).should(($url) => {
        expect($url).to.contain(`/confirmation`)
    })
}

function buildTestName(asWho, region, items, language, payment_method='card') {
    region = region.charAt(0).toUpperCase() + region.toLowerCase().slice(1)
    let lang = 'en'
    if (language) {lang = language}
    return `${asWho} from ${region}, I'm able to buy ${items} and pay with ${payment_method} (language: ${lang})`
}

function verifyLang(language) {
    cy.url().should('contain', `${language}-`)
}

module.exports = {
    testNotLoggedOnUserBuyingFurniture,
    testNotLoggedOnUserBuyingSample,
    testNotLoggedOnUserBuyingFurnitureAndSample,
    testNotLoggedOnUserBuyingFurnitureOnMobile,
    testNotLoggedOnUserBuyingSampleOnMobile,
    testNotLoggedOnUserBuyingFurnitureAndSampleOnMobile,
  }
