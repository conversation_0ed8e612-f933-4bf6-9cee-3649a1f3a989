from celery.schedules import crontab

admin_customization_tasks_scheduler = {
    'email_delivered_but_bad_status': {
        'task': 'admin_customization.tasks.email_delivered_but_bad_status_task',
        'schedule': crontab(minute='0', hour='8'),
    },
    'email_sales_report_check': {
        'task': 'admin_customization.tasks.email_sales_report_check_task',
        'schedule': crontab(minute='59', hour='22'),
    },
    'email_orders_in_production_check': {
        'task': 'admin_customization.tasks.email_orders_in_production_check_task',
        'schedule': crontab(minute='0', hour='8'),
    },
    'email_invoice_numbering_check': {
        'task': 'admin_customization.tasks.email_invoice_numbering_check_task',
        'schedule': crontab(minute='0', hour='8'),
    },
    'money_every_day': {
        'task': 'admin_customization.tasks.money_every_day',
        'schedule': crontab(minute='59', hour='6,9,11,14,17,20,21,22,23'),
    },
    'money_every_day_bf': {
        'task': 'admin_customization.tasks.money_every_day',
        'schedule': crontab(
            minute='14,29,44',
            hour='18,19,20,21,22,23',
            day_of_month='1,29,30',
        ),
    },
    'production_releases': {
        'task': 'admin_customization.tasks.production_releases',
        'schedule': crontab(minute='0', hour='8', day_of_week=1),
    },
}
