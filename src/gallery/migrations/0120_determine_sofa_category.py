# Generated by Django 4.1.13 on 2025-04-24 11:24

from django.db import migrations

from gallery.models import Sotty as SottyFromImport
from gallery.services.category_assigner import determine_sofa_category


def determine_category_for_existing_sotties(apps, schema_editor):
    """
    Warning:
        apps.get_model() loads the model as it existed at the time of the migration
        no custom methods or properties are available unless manually patched.
    """

    Sotty = apps.get_model('gallery', 'Sotty')

    # Manually patch the methods, properties and attributes
    Sotty.get_seating_width = SottyFromImport.get_seating_width
    Sotty.has_extended_chaise_longue_module = (
        SottyFromImport.has_extended_chaise_longue_module
    )
    Sotty.modules_number = SottyFromImport.modules_number

    for sotty in Sotty.objects.all():
        sotty.shelf_category = determine_sofa_category(sotty=sotty)
        sotty.save(update_fields=['shelf_category'])


class Migration(migrations.Migration):

    dependencies = [
        ('gallery', '0119_alter_sotty_shelf_category'),
    ]

    operations = [
        migrations.RunPython(
            determine_category_for_existing_sotties, migrations.RunPython.noop
        ),
    ]
