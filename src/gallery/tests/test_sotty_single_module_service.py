from unittest.mock import patch

import pytest

from custom.enums.colors import Sofa01Color
from gallery.enums import SottyModuleType
from gallery.services.sotty_single_module import SottySingleModuleRepository


@pytest.mark.django_db
class TestSottySingleModuleRepository:
    @pytest.mark.parametrize(
        ('modules', 'expected_module_type'),
        [
            ([{'type': 'armrest'}], SottyModuleType.ARMREST),
            ([{'type': 'open_end'}, {'type': 'seater'}], SottyModuleType.END_FOOTREST),
            ([{'type': 'split'}], SottyModuleType.EXTENDED_CHAISE_LONGUE),
        ],
    )
    def test_get_module_type(self, sotty_factory, modules, expected_module_type):
        sotty = sotty_factory(configurator_params={'layout': [{'modules': modules}]})

        module_type = SottySingleModuleRepository.get_module_type(sotty)
        assert module_type == expected_module_type

    @patch(
        'gallery.services.sotty_single_module.Sotty.objects.get_single_modules_presets'
    )
    def test_get_attributes_map(self, mock, sotty_factory):
        """Test get_attributes_map returns correct structure."""
        sotty = sotty_factory(
            materials=[Sofa01Color.REWOOL2_BROWN],
            width=1_000,
            depth=1_625,
            configurator_params={
                'layout': [{'modules': [{'type': SottyModuleType.CHAISE_LONGUE}]}]
            },
        )
        different_material = {
            'id': 123,
            'module_type': SottyModuleType.CHAISE_LONGUE,
            'material': Sofa01Color.REWOOL2_OLIVE_GREEN,
            'width': 1_000,
            'depth': 1_625,
        }
        different_width_1 = {
            'id': 124,
            'module_type': SottyModuleType.CHAISE_LONGUE,
            'material': Sofa01Color.REWOOL2_BROWN,
            'width': 875,
            'depth': 1_625,
        }
        different_width_2 = {
            'id': 125,
            'module_type': SottyModuleType.CHAISE_LONGUE,
            'material': Sofa01Color.REWOOL2_BROWN,
            'width': 1_125,
            'depth': 1_625,
        }
        wrong_module_type = {
            'id': 999,
            'module_type': SottyModuleType.CHAISE_LONGUE,
            'material': Sofa01Color.REWOOL2_BROWN,
            'width': 1_000,
            'depth': 1_625,
        }
        mock.return_value = [
            {
                'id': sotty.id,
                'module_type': SottyModuleType.CHAISE_LONGUE,
                'material': Sofa01Color.REWOOL2_BROWN,
                'width': 1_000,
                'depth': 1_625,
            },
            different_material,
            different_width_1,
            different_width_2,
            wrong_module_type,
        ]
        repository = SottySingleModuleRepository(sotty)
        attributes_map = repository.get_attributes_map()

        assert attributes_map['material'][0] == sotty.id
        assert attributes_map['material'][1] == different_material['id']
        for material_id in range(2, 15):
            assert attributes_map['material'][material_id] is None
        assert attributes_map['width'] == {
            875: different_width_1['id'],
            1_000: sotty.id,
            1_125: different_width_2['id'],
        }
        assert 'depth' not in attributes_map
