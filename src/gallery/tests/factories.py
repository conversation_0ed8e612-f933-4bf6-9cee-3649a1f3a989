import json

from random import randint

from django.contrib.contenttypes.models import ContentType
from django.core.files.uploadedfile import SimpleUploadedFile
from django.db.models import signals
from django.db.models.signals import post_save

import factory

from factory import fuzzy

from custom.enums import (
    SampleBoxVariantEnum,
    ShelfType,
)
from custom.enums.colors import Sofa01Color
from gallery.enums import (
    ConfiguratorTypeEnum,
    FurnitureCategory,
    SottyModuleType,
)


def get_element():
    return {
        'x1': fuzzy.FuzzyInteger(low=-100, high=100).fuzz(),
        'x2': fuzzy.FuzzyInteger(low=-100, high=100).fuzz(),
        'y1': fuzzy.FuzzyInteger(low=-100, high=100).fuzz(),
        'y2': fuzzy.FuzzyInteger(low=-100, high=100).fuzz(),
        'z1': fuzzy.FuzzyInteger(low=-100, high=100).fuzz(),
        'z2': fuzzy.FuzzyInteger(low=-100, high=100).fuzz(),
        'depth': fuzzy.FuzzyInteger(low=-100, high=100).fuzz(),
        'height': fuzzy.FuzzyInteger(low=-100, high=100).fuzz(),
    }


def get_sotty_element(material: int):
    return {
        'id': 'S01_AR_D07_W02',
        'height': 835,  # constant for seaters
        'width': fuzzy.FuzzyChoice((750, 880, 1_000, 1_130)).fuzz(),
        'depth': fuzzy.FuzzyChoice((1_000, 1_125)).fuzz(),
        'x1': fuzzy.FuzzyInteger(low=-100, high=100).fuzz(),
        'x2': fuzzy.FuzzyInteger(low=-100, high=100).fuzz(),
        'y1': fuzzy.FuzzyInteger(low=-100, high=100).fuzz(),
        'y2': fuzzy.FuzzyInteger(low=-100, high=100).fuzz(),
        'z1': 0,
        'z2': fuzzy.FuzzyInteger(low=0, high=100).fuzz(),
        'material': material,
        'material_backrest': material,
        'material_cushion': material,
    }


def get_module_element(material: int, type_: str | None):
    module_type_choices = [
        'armrest',
        'chaise_longue',
        'seater',
        'corner',
        'footrest',
        'open_end',
        'open_middle',
    ]
    type_ = type_ or fuzzy.FuzzyChoice(choices=module_type_choices).fuzz()
    return {
        'type': type_,
        'depth': fuzzy.FuzzyChoice(choices=[1_000, 1_125, 1_500]).fuzz(),
        'width': fuzzy.FuzzyChoice(choices=[750, 880, 1_000, 1_130]).fuzz(),
        'material': material,
        'material_cushion': material,
        'material_backrest': material,
    }


def get_layout_element(material: int, module_type: str | None = None):
    return {
        'type': fuzzy.FuzzyChoice(choices=SottyModuleType.choices).fuzz(),
        'modules': [get_module_element(material=material, type_=module_type)],
        'direction': {'in': ['left'], 'out': ['right']},
    }


def get_configurator_param(material: int, **kwargs):
    if kwargs.get('single_extended_chaise_longue', False):
        layout = [
            get_layout_element(
                material=material, module_type=SottyModuleType.EXTENDED_CHAISE_LONGUE
            )
        ]
    else:
        layout = [get_layout_element(material=material) for _ in range(2)]
    if kwargs.get('extended_chaise_longue', False):
        layout.append(
            get_layout_element(
                material=material, module_type=SottyModuleType.EXTENDED_CHAISE_LONGUE
            )
        )
    return {
        'width': 4000,
        'layout': layout,
        'armrests': 'both',
        'footrest': True,
        'material': {
            'material': material,
            'material_cushion': material,
            'material_backrest': material,
        },
        'direction': 'right',
        'shelf_type': 10,
        'symmetrical': False,
        'width_left': 0,
        'covers_only': False,
        'layout_type': 'l-shape',
        'width_right': 2750,
        'decoder_params': {
            'default_material': {
                'material': material,
                'material_cushion': material,
                'material_backrest': material,
            },
        },
    }


def sample_box_variants_custom_excluded():
    custom_variants = [SampleBoxVariantEnum.CUSTOM]
    return [
        variant
        for variant in SampleBoxVariantEnum.choices()
        if variant not in custom_variants
    ]


@factory.django.mute_signals(post_save)
class JettyFactory(factory.django.DjangoModelFactory):
    # NOTE: please add new fields alphabetically, for easier navigation
    depth = 320
    doors = [get_element() for _ in range(3)]
    drawers = [get_element() for _ in range(3)]
    horizontals = [get_element() for _ in range(3)]
    material = 0
    owner = factory.SubFactory('user_profile.tests.factories.UserFactory')
    pattern = 0
    rows = factory.ListFactory()
    shelf_type = 0
    supports = [get_element() for _ in range(3)]
    verticals = [get_element() for _ in range(3)]
    preview = factory.django.FileField(filename='the_file.webp', data=b'abc')
    shelf_category = FurnitureCategory.BOOKCASE
    width = 0
    height = 0
    max_capacity = 1

    class Meta:
        model = 'gallery.Jetty'


class SampleBoxFactory(factory.django.DjangoModelFactory):
    owner = factory.SubFactory('user_profile.tests.factories.UserFactory')
    box_variant = factory.SubFactory(
        'warehouse.tests.factories.SampleBoxVariantFactory'
    )

    class Meta:
        model = 'gallery.SampleBox'


@factory.django.mute_signals(signals.pre_save)
class CustomDnaFactory(factory.django.DjangoModelFactory):
    author = factory.SubFactory('user_profile.tests.factories.UserFactory')
    visible_on_web = True
    configurator_type = 2
    pattern_slot = 0
    shelf_type = 0
    dna_objects = [231, 321, 123]

    class Meta:
        model = 'gallery.CustomDna'

    @factory.lazy_attribute
    def dna(self):
        if self.configurator_type in {
            ConfiguratorTypeEnum.COLUMN,
            ConfiguratorTypeEnum.MIXED_ROW_COLUMN,
        }:
            meshes = {}
            for mesh_id in self.dna_objects:
                meshes[str(mesh_id)] = {'constants': {'pattern': randint(1, 4)}}
            content = {
                'superior_object_ids': self.dna_objects,
                'serialization': {'mesh': meshes},
            }
            filename = f'dna{self.dna_objects[0]}.json'
        else:
            content = {'old_dna': 'yep'}
            filename = f'{self.pattern_slot}, {self.shelf_type}'
        return SimpleUploadedFile(filename, json.dumps(content).encode())


@factory.django.mute_signals(post_save)
class WattyFactory(factory.django.DjangoModelFactory):
    depth = 320
    height = 300
    owner = factory.SubFactory('user_profile.tests.factories.UserFactory')
    width = 200
    shelf_type = ShelfType.TYPE03
    material = 0
    preview = factory.django.FileField(filename='the_file_watty.webp', data=b'abc')

    class Meta:
        model = 'gallery.Watty'

    class Params:
        is_tone_wardrobe = factory.Trait(
            shelf_type=ShelfType.TYPE03, shelf_category=FurnitureCategory.WARDROBE
        )
        is_edge_wardrobe = factory.Trait(
            shelf_type=ShelfType.TYPE13, shelf_category=FurnitureCategory.WARDROBE
        )


@factory.django.mute_signals(post_save)
class SottyFactory(factory.django.DjangoModelFactory):
    depth = 1_600
    height = 830
    width = 3_500
    armrests = factory.LazyAttribute(
        lambda obj: [get_sotty_element(material=obj.materials[0])]
    )
    chaise_longues = factory.LazyAttribute(
        lambda obj: [get_sotty_element(material=obj.materials[0])]
    )
    corners = []
    footrests = factory.LazyAttribute(
        lambda obj: [get_sotty_element(material=obj.materials[0])]
    )
    seaters = factory.LazyAttribute(
        lambda obj: [get_sotty_element(material=obj.materials[0]) for _ in range(2)]
    )
    materials = [Sofa01Color.REWOOL2_BROWN]
    owner = factory.SubFactory('user_profile.tests.factories.UserFactory')
    preview = factory.django.FileField(filename='the_file_watty.webp', data=b'abc')
    configurator_params = factory.LazyAttribute(
        lambda obj: get_configurator_param(material=obj.materials[0])
    )

    class Meta:
        model = 'gallery.Sotty'

    class Params:
        corner = factory.Trait(
            materials=[Sofa01Color.REWOOL2_BROWN],
            corners=[get_sotty_element(material=Sofa01Color.REWOOL2_BROWN)],
        )
        extended_chaise_longue = factory.Trait(
            materials=[Sofa01Color.REWOOL2_BROWN],
            configurator_params=get_configurator_param(
                material=Sofa01Color.REWOOL2_BROWN, extended_chaise_longue=True
            ),
        )
        single_extended_chaise_longue = factory.Trait(
            materials=[Sofa01Color.REWOOL2_BROWN],
            configurator_params=get_configurator_param(
                material=Sofa01Color.REWOOL2_BROWN,
                single_extended_chaise_longue=True,
            ),
        )
        one_module = factory.Trait(
            materials=[Sofa01Color.REWOOL2_BROWN],
            corners=[
                get_sotty_element(material=Sofa01Color.REWOOL2_BROWN) for _ in range(1)
            ],
            chaise_longues=[],
            seaters=[],
            footrests=[],
        )
        two_modules = factory.Trait(
            materials=[Sofa01Color.REWOOL2_BROWN],
            corners=[
                get_sotty_element(material=Sofa01Color.REWOOL2_BROWN) for _ in range(2)
            ],
            chaise_longues=[],
            seaters=[],
            footrests=[],
        )
        three_modules = factory.Trait(
            materials=[Sofa01Color.REWOOL2_BROWN],
            corners=[
                get_sotty_element(material=Sofa01Color.REWOOL2_BROWN) for _ in range(3)
            ],
            chaise_longues=[],
            seaters=[],
            footrests=[],
        )


class FurnitureGridImageFactory(factory.django.DjangoModelFactory):
    content_type = factory.LazyAttribute(
        lambda instance: ContentType.objects.get_for_model(instance.furniture),
    )
    object_id = factory.SelfAttribute('.furniture.id')
    furniture = factory.SubFactory('gallery.tests.factories.JettyFactory')
    color = 0
    image = factory.django.ImageField()

    class Meta:
        model = 'gallery.FurnitureGridImage'


class FurnitureImageFactory(factory.django.DjangoModelFactory):
    furniture_content_type = factory.LazyAttribute(
        lambda instance: ContentType.objects.get_for_model(instance.furniture),
    )
    furniture_object_id = factory.SelfAttribute('.furniture.id')
    furniture = factory.SubFactory('gallery.tests.factories.JettyFactory')
    image = factory.django.ImageField()

    class Meta:
        model = 'gallery.FurnitureImage'
