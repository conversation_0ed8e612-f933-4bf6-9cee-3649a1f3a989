import json

from decimal import Decimal
from pathlib import Path
from unittest import mock
from unittest.mock import patch

from django.conf import settings
from django.contrib.auth.models import User
from django.core.files.base import ContentFile
from django.urls import reverse

import factory
import pytest
import requests_mock

from pytest_cases import parametrize_with_cases
from rest_framework import status

from carts.services.cart_service import CartService
from custom.enums import (
    Furniture,
    LanguageEnum,
    Sofa01Color,
)
from events.choices import (
    BrazeSubscriptionGroupStatus,
    BrazeSubscriptionStates,
)
from events.models import Event
from gallery.enums import FurnitureImageType  # noqa: F401
from gallery.enums import (
    CapeCollectionType,
    ConfiguratorTypeEnum,
    FurnitureCategory,
    FurnitureStatusEnum,
    SottyModuleType,
)
from gallery.models import (
    Jetty,
    SampleBox,
    Sotty,
    Watty,
)
from gallery.serializers import WattySerializer
from gallery.services.sotty_single_module import SottySingleModuleRepository
from gallery.tests.factories import JettyFactory
from orders.choices import OrderSource
from pricing_v3.recycle_tax_rates import get_recycle_tax_value
from user_profile.choices import SubscriptionSources
from user_profile.models import UserProspect


def wishlist_service_mock():
    mock_instance = mock.MagicMock()
    mock_instance.return_value.user_from_email = False
    mock_instance.add_to_wishlist_popup = mock.MagicMock()
    return mock_instance


@pytest.mark.facebook
@pytest.mark.django_db
class TestFurnitureViewSet:
    graph_url = '//graph.facebook.com'

    @pytest.mark.parametrize(
        ('model_name', 'model_class', 'url_name', 'furniture_data'),
        [
            ('jetty', Jetty, 'jetty-add-to-cart', 'jetty_data'),
            ('watty', Watty, 'watty-add-to-cart', 'watty_data'),
            ('sotty', Sotty, 'sotty-add-to-cart', 'sotty_data'),
        ],
    )
    def test_authorized_user_can_add_to_cart(
        self,
        api_client,
        user,
        region_de,
        request,
        model_name,
        model_class,
        url_name,
        furniture_data,
    ):
        user.profile.region = region_de
        user.profile.save(update_fields=['region'])
        api_client.force_authenticate(user=user)
        data = request.getfixturevalue(furniture_data)
        url = reverse(url_name)

        response = api_client.post(url, data, format='json')

        assert response.status_code == status.HTTP_201_CREATED
        related_set_name = f'{model_name}_set'
        assert getattr(user, related_set_name).count() == 1
        assert user.carts.count() == 1

    @patch('gallery.views.FurnitureViewSet._handle_preview')
    @patch('gallery.views.WishlistService', new_callable=wishlist_service_mock)
    @pytest.mark.parametrize(
        ('model_name', 'model_class', 'url_name', 'furniture_data'),
        [
            ('jetty', Jetty, 'jetty-add-by-geom-to-wishlist-popup', 'jetty_data'),
            ('watty', Watty, 'watty-add-by-geom-to-wishlist-popup', 'watty_data'),
            ('sotty', Sotty, 'sotty-add-by-geom-to-wishlist-popup', 'sotty_data'),
        ],
    )
    def test_authorized_user_can_add_to_wishlist_popup(
        self,
        _,
        __,
        api_client,
        user,
        request,
        model_name,
        model_class,
        url_name,
        furniture_data,
    ):
        api_client.force_authenticate(user=user)
        data = request.getfixturevalue(furniture_data)
        data['email'] = user.email
        url = reverse(url_name)

        response = api_client.post(url, data, format='json')

        assert response.status_code == status.HTTP_200_OK
        response_json = response.json()
        assert (
            response_json['id']
            == getattr(user, f'{model_name}_set')
            .filter(furniture_status=FurnitureStatusEnum.SAVED)
            .first()
            .id
        )
        assert response_json['user_exist'] is False
        assert response_json['already_registered'] is False

    @patch('gallery.views.emit_saved_item_related_events')
    @pytest.mark.parametrize(
        ('furniture_factory', 'url_name'),
        [
            ('jetty_factory', 'jetty-save-for-later-by-id-popup'),
            ('watty_factory', 'watty-save-for-later-by-id-popup'),
            ('sotty_factory', 'sotty-save-for-later-by-id-popup'),
        ],
    )
    def test_save_for_later_by_id_popup_emits_events(
        self,
        mock_emit,
        api_client,
        request,
        furniture_factory,
        url_name,
    ):
        furniture = request.getfixturevalue(furniture_factory)(preset=True)
        email = '<EMAIL>'
        real_ip = '*************'
        data = {'email': email, 'marketing_permission': True}
        url = reverse(url_name, kwargs={'pk': furniture.id})

        response = api_client.post(url, data, format='json', HTTP_X_REAL_IP=real_ip)

        assert response.status_code == status.HTTP_200_OK, furniture.id
        mock_emit.assert_called_once()
        assert mock_emit.call_args[1] == {
            'item': furniture,
            'email': email,
            'source': SubscriptionSources.APP,
            'marketing_permission': True,
        }
        response_json = response.json()
        assert response_json == {'already_registered': False, 'status': 'ok'}
        assert UserProspect.objects.filter(
            prospect_source=OrderSource.MOBILE_NATIVE_IOS,
            email=email,
            origin='save for later app',
            description=real_ip,
        ).exists()

    def test_guest_customer_add_to_wishlist_popup_updates_guest_email(
        self,
        api_client,
        guest_user,
        jetty_data,
    ):
        """Ensure ``request.user.email`` is updated when user is guest."""
        with requests_mock.Mocker() as mock_request:
            mock_request.post(self.graph_url)
            email = '<EMAIL>'
            assert guest_user.email != email

            api_client.force_authenticate(user=guest_user)
            response = api_client.post(
                path=reverse('jetty-add-by-geom-to-wishlist-popup'),
                data={**jetty_data, 'email': email},
                format='json',
            )
            assert response.status_code == status.HTTP_200_OK
            assert guest_user.email == email

    def test_guest_customer_add_to_wishlist_popup_price_with_discount_value(
        self,
        api_client,
        guest_user,
        jetty_data,
    ):
        """Ensure ``request.user.email`` is updated when user is guest."""
        with requests_mock.Mocker() as mock_request:
            mock_request.post(self.graph_url)
            email = '<EMAIL>'
            assert guest_user.email != email

            api_client.force_authenticate(user=guest_user)
            response = api_client.post(
                path=reverse('jetty-add-by-geom-to-wishlist-popup'),
                data={**jetty_data, 'email': email},
                format='json',
            )
            assert response.status_code == status.HTTP_200_OK
            assert response.data['price_with_discount'] is not None

    def test_add_to_wishlist_popup_on_behalf_of_non_existing_user_dont_update_user_email(
        self,
        api_client,
        user,
        jetty_data,
    ):
        """
        Test for fix of bug: https://sentry.io/organizations/tylko/issues/1566679898/.
        """
        with requests_mock.Mocker() as mock_request:
            mock_request.post(self.graph_url)
            email = '<EMAIL>'
            api_client.force_authenticate(user=user)
            response = api_client.post(
                path=reverse('jetty-add-by-geom-to-wishlist-popup'),
                data={**jetty_data, 'email': email},
                format='json',
            )
            assert response.status_code == status.HTTP_200_OK
            assert user.email != email

    @pytest.mark.parametrize(
        ('field', 'expected_status_code'),
        [
            ('username', status.HTTP_201_CREATED),
            ('email', status.HTTP_200_OK),
        ],
    )
    def test_add_to_wishlist_popup_on_behalf_of_existing_user_dont_update_user_email(
        self,
        field,
        expected_status_code,
        api_client,
        user_factory,
        user,
        jetty_data,
    ):
        with requests_mock.Mocker() as mock_request:
            mock_request.post(self.graph_url)
            email = '<EMAIL>'
            existing_user = user_factory(**{field: email})
            api_client.force_authenticate(user)
            response = api_client.post(
                path=reverse('jetty-add-by-geom-to-wishlist-popup'),
                data={**jetty_data, 'email': email},
                format='json',
            )
            assert response.status_code == expected_status_code

            existing_user.refresh_from_db()
            assert user.email != email
            assert existing_user.email == email

    @patch(
        'events.services.subscription_event_handler.'
        'BrazeClient.get_email_subscription_group_status',
        return_value=BrazeSubscriptionGroupStatus.UNSUBSCRIBED,
    )
    def test_add_to_wishlist_popup_emits_subscription_events_if_marketing_permission(
        self,
        _,
        user,
        api_client,
    ):
        generic_jetty = factory.build(dict, FACTORY_CLASS=JettyFactory)
        generic_jetty.pop('owner')
        generic_jetty.pop('preview')
        url = reverse('jetty-add-by-geom-to-wishlist-popup')
        data = {
            'email': '<EMAIL>',
            'geom': generic_jetty,
            'marketing_permission': True,
            'outside_eu': '0',
            'popup_src': 'mobile exit section',
        }
        api_client.force_login(user)
        api_client.post(url, data, format='json')

        events = Event.objects.all()
        assert events.filter(event_name='EmailSubscriptionEvent').exists()
        assert events.filter(event_name='EmailSubscriptionStatusUpdateEvent').exists()

    @patch(
        'events.services.subscription_event_handler.'
        'BrazeClient.get_email_subscription_group_status',
        return_value=BrazeSubscriptionGroupStatus.UNSUBSCRIBED,
    )
    def test_save_for_later_by_id_popup_emits_subscription_events_if_marketing_permission(
        self,
        _,
        api_client,
        jetty,
    ):
        url = reverse('jetty-save-for-later-by-id-popup', kwargs={'pk': jetty.id})
        data = {'email': '<EMAIL>', 'marketing_permission': True}

        api_client.force_login(jetty.owner)
        response = api_client.post(url, data, format='json')
        assert response.status_code == status.HTTP_200_OK

        events = Event.objects.all()
        assert events.filter(event_name='EmailSubscriptionEvent').exists()
        assert events.filter(event_name='EmailSubscriptionStatusUpdateEvent').exists()

    @pytest.mark.parametrize(
        ('furniture_factory', 'furniture_basename'),
        (
            ('jetty_factory', 'jetty-detail'),
            ('sotty_factory', 'sotty-detail'),
            ('watty_factory', 'watty-detail'),
        ),
    )
    def test_deleting_last_item_from_wishlist_emits_wishlist_empty_event(
        self,
        furniture_factory,
        furniture_basename,
        request,
        api_client,
        user,
    ):
        furniture_factory = request.getfixturevalue(furniture_factory)
        furniture = furniture_factory.create(
            owner=user,
            furniture_status=FurnitureStatusEnum.SAVED,
        )
        assert user.profile.get_library_item_number() == 1

        url = reverse(furniture_basename, args=(furniture.id,))
        api_client.force_authenticate(user)
        response = api_client.delete(url)
        assert response.status_code == status.HTTP_200_OK

        assert user.profile.get_library_item_number() == 0
        assert Event.objects.filter(event_name='WishlistEmptyEvent').exists()

    @pytest.mark.parametrize(
        ('furniture_factory', 'furniture_basename'),
        (
            ('jetty_factory', 'jetty-detail'),
            ('sotty_factory', 'sotty-detail'),
            ('watty_factory', 'watty-detail'),
        ),
    )
    def test_deleting_not_last_item_does_not_emits_wishlist_empty_event(
        self,
        furniture_factory,
        furniture_basename,
        request,
        api_client,
        user,
    ):
        furniture_factory = request.getfixturevalue(furniture_factory)
        furniture, _ = furniture_factory.create_batch(
            2,
            owner=user,
            furniture_status=FurnitureStatusEnum.SAVED,
        )
        assert user.profile.get_library_item_number() == 2

        url = reverse(furniture_basename, args=(furniture.id,))
        api_client.force_authenticate(user)
        response = api_client.delete(url)
        assert response.status_code == status.HTTP_200_OK

        assert user.profile.get_library_item_number() == 1
        assert not Event.objects.filter(event_name='WishlistEmptyEvent').exists()

    @patch(
        'events.services.subscription_event_handler.BrazeClient.'
        'get_email_subscription_group_status',
        return_value=BrazeSubscriptionGroupStatus.UNSUBSCRIBED,
    )
    def test_register_anonymous_user_on_create(self, _, api_client, jetty_data):
        url = reverse('jetty-list')
        user_count_before = User.objects.count()

        response = api_client.post(url, jetty_data, format='json')
        assert response.status_code == status.HTTP_201_CREATED
        assert User.objects.count() == user_count_before + 1

        created_user = User.objects.last()
        created_shelf = Jetty.objects.get(id=response.json()['id'])
        assert created_shelf.owner == created_user

    @patch('gallery.models.Jetty.set_preview')
    def test_register_anonymous_user_on_share_on_social_media_popup(
        self,
        _,
        api_client,
        jetty_data,
    ):
        jetty_data['magic_preview'] = 'abc'
        url = reverse('jetty-share-on-social-media-popup', args=(1,))
        user_count_before = User.objects.count()

        response = api_client.post(url, jetty_data, format='json')
        assert response.status_code == status.HTTP_200_OK
        assert User.objects.count() == user_count_before + 1

        created_user = User.objects.last()
        created_shelf_id = response.json()['id']
        created_shelf = Jetty.objects.get(id=created_shelf_id)
        assert created_shelf.owner == created_user

    @patch('gallery.models.Jetty.set_preview')
    def test_register_anonymous_user_on_add_to_wishlist_popup(
        self,
        _,
        api_client,
        jetty_data,
    ):
        url = reverse('jetty-add-by-geom-to-wishlist-popup')
        user_count_before = User.objects.count()
        data = {'email': '<EMAIL>', **jetty_data, 'magic_preview': 'abc'}

        response = api_client.post(url, data, format='json')
        assert response.status_code == status.HTTP_200_OK
        assert User.objects.count() == user_count_before + 1

        created_user = User.objects.last()
        created_shelf_id = response.json()['id']
        created_shelf = Jetty.objects.get(id=created_shelf_id)
        assert created_shelf.owner == created_user

    def test_register_anonymous_user_on_add_to_waiting_list(
        self,
        api_client,
        watty,
    ):
        data = WattySerializer(watty).data
        data['email'] = '<EMAIL>'
        data['configurator_params']['geom_id'] = 1
        data['preview'] = None
        url = reverse('watty-add-to-waiting-list')
        user_count_before = User.objects.count()

        response = api_client.post(url, data, format='json')
        assert response.status_code == status.HTTP_201_CREATED
        assert User.objects.count() == user_count_before + 1

        created_user = User.objects.last()
        created_watty_id = response.json()['id']
        created_watty = Watty.objects.get(id=created_watty_id)
        assert created_watty.owner == created_user

    @pytest.mark.parametrize('furniture_type', ['jetty', 'sotty', 'watty'])
    @patch(
        'user_profile.models.LoginAccessToken.get_or_create_for_user',
        return_value='abc',
    )
    @patch(
        'events.services.subscription_event_handler.BrazeClient.'
        'get_email_subscription_group_status',
        return_value=BrazeSubscriptionGroupStatus.UNSUBSCRIBED,
    )
    def test_emit_wishlist_add_event(
        self,
        _,
        __,
        furniture_type,
        api_client,
        user,
        jetty_data,
        sotty_data,
        watty_data,
    ):
        data_mapping = {
            'jetty': jetty_data,
            'sotty': sotty_data,
            'watty': watty_data,
        }
        url = f'{furniture_type}-add-by-geom-to-wishlist'

        api_client.force_authenticate(user=user)
        response = api_client.post(
            path=reverse(url),
            data=data_mapping[furniture_type],
            format='json',
        )
        assert response.status_code == status.HTTP_201_CREATED

        created_furniture_data = response.json()
        event = Event.objects.filter(event_name='WishlistAddEvent').last()

        assert event.properties['user_id'] == user.id
        assert event.properties['furniture_id'] == created_furniture_data['id']
        assert event.properties['furniture_type'] == (
            created_furniture_data['furniture_type']
        )
        assert event.properties['lat'] == 'abc'

    @pytest.mark.parametrize('furniture_type', ['jetty', 'watty'])
    @patch(
        'user_profile.models.LoginAccessToken.get_or_create_for_user',
        return_value='abc',
    )
    @patch(
        'events.services.subscription_event_handler.BrazeClient.'
        'get_email_subscription_group_status',
        return_value=BrazeSubscriptionGroupStatus.UNSUBSCRIBED,
    )
    def test_should_change_furniture_status_when_adding_by_id_to_wishlist(
        self,
        _,
        __,
        furniture_type,
        api_client,
        user,
        jetty_factory,
        sotty_factory,
        watty_factory,
    ):
        # Assign
        furniture = {
            'jetty': jetty_factory,
            'sotty': sotty_factory,
            'watty': watty_factory,
        }.get(furniture_type)(owner=user)
        url = reverse(
            f'{furniture_type}-add-by-id-to-wishlist', kwargs={'pk': furniture.id}
        )
        api_client.force_authenticate(user=user)

        # Act
        response = api_client.post(path=url)

        # Assert
        assert response.status_code == status.HTTP_201_CREATED
        response_json = response.json()
        furniture_model = Furniture(furniture_type).model
        assert furniture_model.objects.filter(
            id=response_json['created_furniture_id'],
            owner_id=user.id,
            furniture_status=FurnitureStatusEnum.SAVED,
        ).exists()

    @pytest.mark.parametrize('furniture_type', ['jetty', 'sotty', 'watty'])
    @patch(
        'user_profile.models.LoginAccessToken.get_or_create_for_user',
        return_value='abc',
    )
    @patch('gallery.services.add_to_wishlist.WishlistService.add_to_wishlist_popup')
    def test_should_change_furniture_status_when_adding_by_id_to_wishlist_popup(
        self,
        _,
        __,
        furniture_type,
        api_client,
        user,
        jetty_factory,
        sotty_factory,
        watty_factory,
    ):
        # Assign
        furniture = {
            'jetty': jetty_factory,
            'sotty': sotty_factory,
            'watty': watty_factory,
        }.get(furniture_type)(preset=True)
        url = reverse(
            f'{furniture_type}-add-by-id-to-wishlist-popup', kwargs={'pk': furniture.id}
        )
        api_client.force_authenticate(user=user)

        # Act
        response = api_client.post(path=url, data={'email': '<EMAIL>'})

        # Assert
        assert response.status_code == status.HTTP_201_CREATED
        response_json = response.json()
        furniture_model = Furniture(furniture_type).model
        assert furniture_model.objects.filter(
            id=response_json['created_furniture_id'],
            owner_id=user.id,
            furniture_status=FurnitureStatusEnum.SAVED,
        ).exists()
        assert response_json['user_exist'] is False
        assert response_json['already_registered'] is False

    @pytest.mark.parametrize('furniture_type', ['jetty', 'sotty', 'watty'])
    @patch(
        'events.services.subscription_event_handler.BrazeClient.'
        'get_email_subscription_group_status',
        return_value=BrazeSubscriptionGroupStatus.UNSUBSCRIBED,
    )
    def test_subscribes_to_s4l_subscription_group_when_adding_to_the_wishlist(
        self,
        _,
        settings,
        furniture_type,
        api_client,
        user_factory,
        jetty_data,
        sotty_data,
        watty_data,
    ):
        user = user_factory(email='<EMAIL>')
        settings.BRAZE_EMAIL_SUBSCRIPTION_GROUPS['save_for_later'] = 'abc'
        data_mapping = {
            'jetty': jetty_data,
            'sotty': sotty_data,
            'watty': watty_data,
        }
        url = f'{furniture_type}-add-by-geom-to-wishlist'

        api_client.force_authenticate(user=user)
        response = api_client.post(
            path=reverse(url),
            data=data_mapping[furniture_type],
            format='json',
        )
        assert response.status_code == status.HTTP_201_CREATED
        event = Event.objects.filter(event_name='EmailSubscriptionEvent').last()

        assert event.properties['subscription_group'] == 'abc'
        assert event.properties['subscription_state'] == (
            BrazeSubscriptionStates.SUBSCRIBED
        )
        assert event.properties['email'] == '<EMAIL>'


@pytest.mark.django_db
class TestWaitingListAction:
    @pytest.mark.parametrize(
        'endpoint', ['jetty-add-to-waiting-list', 'samplebox-add-to-waiting-list']
    )
    def test_add_non_watty_to_waiting_list_raises_exception(self, endpoint, api_client):
        response = api_client.post(reverse(endpoint))

        assert response.status_code == status.HTTP_400_BAD_REQUEST
        assert response.json()['error_codes'] == 'unsupported_model'

    def test_add_watty_to_waiting_list_saves_object(
        self,
        api_client,
        watty_factory,
        region_factory,
    ):
        region = region_factory(germany=True)
        watty = watty_factory(preview=ContentFile('abc'))
        data = WattySerializer(
            watty,
            context={'region': region.cached_region_data},
        ).data
        data['email'] = '<EMAIL>'
        data['configurator_params']['geom_id'] = 1

        url = reverse('watty-add-to-waiting-list')
        response = api_client.post(url, data, format='json')

        assert response.status_code == status.HTTP_201_CREATED
        assert response.json()['furniture_status'] == 'special'


@pytest.mark.django_db
class TestCustomDnaPreviewView:
    @pytest.mark.parametrize(
        ['configurator_type', 'collection_type', 'expected'],
        [
            [ConfiguratorTypeEnum.ROW, CapeCollectionType.SIDEBOARD, 'jetty'],
            [ConfiguratorTypeEnum.COLUMN, CapeCollectionType.SHOE_RACK, 'jetty'],
            [
                ConfiguratorTypeEnum.MIXED_ROW_COLUMN,
                CapeCollectionType.WARDROBE,
                'watty',
            ],
        ],
    )
    def test_returns_appropriate_model(
        self,
        configurator_type,
        collection_type,
        expected,
        custom_dna_factory,
        jetty_factory,
        user,
        api_client,
    ):
        jetty_factory(id=7645)  # hardcoded Jetty ID for row configurator
        custom_dna = self._get_custom_dna(
            custom_dna_factory,
            configurator_type,
            collection_type,
        )
        url = reverse('custom-dna-preview', kwargs={'pk': custom_dna.id})
        api_client.force_authenticate(user)
        response = api_client.get(url)
        assert response.json()['furniture_type'] == expected

    def test_include_override_dna_jsons_in_response(
        self,
        custom_dna_factory,
        user,
        api_client,
    ):
        custom_dna = self._get_custom_dna(custom_dna_factory)
        url = reverse('custom-dna-preview', kwargs={'pk': custom_dna.id})
        api_client.force_authenticate(user)

        response = api_client.get(url)
        assert 'override_dna_json' in response.json()

    @staticmethod
    def _get_custom_dna(
        custom_dna_factory,
        configurator_type=ConfiguratorTypeEnum.COLUMN,
        collection_type=CapeCollectionType.SIDEBOARD,
    ):
        custom_dna = custom_dna_factory(
            configurator_type=configurator_type,
            shelf_type=3 if collection_type == CapeCollectionType.WARDROBE else 0,
        )
        with open(custom_dna.dna.path, 'r+') as fp:
            dna = json.load(fp)
            fp.seek(0)
            fp.truncate()
            dna['superior_object_collection'] = collection_type
            fp.write(json.dumps(dna))
        return custom_dna


@pytest.mark.django_db
class TestMailingSavedItemAPIView:
    def test_response_200_for_jetty(
        self, user_factory, jetty_factory, country_factory, api_client
    ):
        germany = country_factory(germany=True)
        user = user_factory(
            profile__language=LanguageEnum.EN, profile__region=germany.region
        )
        saved_jetty = jetty_factory(
            owner=user,
            furniture_status=FurnitureStatusEnum.SAVED,
        )
        url = reverse('jetty-mailing-saved', kwargs={'pk': saved_jetty.id})

        api_client.force_authenticate(user)
        headers = {'HTTP_BRAZE_APP_ID': 'app_id', 'HTTP_BRAZE_API_KEY': 'api_key'}
        response = api_client.get(url, **headers)

        assert response.status_code == status.HTTP_200_OK
        assert response.json()['image_url'] == f'{saved_jetty.preview.url}'
        assert response.json()['item_url'] == (
            f'/en-de/furniture/bookcases/{saved_jetty.id},j,/'
        )
        assert response.json()['description'] == {
            'name': 'Tylko Shelf',
            'material': 'White Plywood',
            'dimensions': 'H0cm, W0, D32cm',
        }

    def test_response_404_if_jetty_status_not_saved(self, api_client, jetty_factory):
        jetty = jetty_factory(furniture_status=FurnitureStatusEnum.DRAFT)
        url = reverse('jetty-mailing-saved', kwargs={'pk': jetty.id})
        api_client.force_authenticate(jetty.owner)
        headers = {'HTTP_BRAZE_APP_ID': 'app_id', 'HTTP_BRAZE_API_KEY': 'api_key'}
        response = api_client.get(url, **headers)

        assert response.status_code == status.HTTP_404_NOT_FOUND

    def test_response_200_for_watty(
        self, user_factory, watty_factory, country_factory, api_client
    ):
        germany = country_factory(germany=True)
        user = user_factory(
            profile__language=LanguageEnum.EN, profile__region=germany.region
        )
        saved_watty = watty_factory(
            owner=user,
            furniture_status=FurnitureStatusEnum.SAVED,
        )
        url = reverse('watty-mailing-saved', kwargs={'pk': saved_watty.id})

        api_client.force_authenticate(user)
        headers = {'HTTP_BRAZE_APP_ID': 'app_id', 'HTTP_BRAZE_API_KEY': 'api_key'}
        response = api_client.get(url, **headers)

        assert response.status_code == status.HTTP_200_OK
        assert response.json()['image_url'] == f'{saved_watty.preview.url}'
        assert (
            response.json()['item_url']
            == f'/en-de/furniture/wardrobe/{saved_watty.id},w,/'
        )
        assert response.json()['description'] == {
            'name': 'Wardrobe',
            'material': 'White',
            'dimensions': 'H30cm, W20cm, D32cm',
        }

    def test_response_404_if_watty_status_not_saved(self, api_client, watty_factory):
        watty = watty_factory(furniture_status=FurnitureStatusEnum.DRAFT)
        url = reverse('watty-mailing-saved', kwargs={'pk': watty.id})
        api_client.force_authenticate(watty.owner)
        headers = {'HTTP_BRAZE_APP_ID': 'app_id', 'HTTP_BRAZE_API_KEY': 'api_key'}
        response = api_client.get(url, **headers)

        assert response.status_code == status.HTTP_404_NOT_FOUND


@pytest.mark.django_db
class TestWishlistVew:
    @pytest.mark.parametrize(
        'furniture_factory', ('jetty_factory', 'sotty_factory', 'watty_factory')
    )
    def test_wishlist_get_response_200(
        self,
        user_factory,
        furniture_factory,
        api_client,
        request,
        region_de,
        country_factory,
    ):
        furniture_factory = request.getfixturevalue(furniture_factory)
        region_de.countries.add(country_factory(germany=True))
        user = user_factory(
            profile__language=LanguageEnum.EN, profile__region=region_de
        )
        saved_furniture = furniture_factory(
            owner=user,
            furniture_status=FurnitureStatusEnum.SAVED,
        )
        api_client.force_authenticate(user)
        response = api_client.get(reverse('wishlist-list'))
        assert response.status_code == status.HTTP_200_OK
        assert response.json()['results'][0]['id'] == saved_furniture.id

    def test_max_num_queries(
        self,
        user_factory,
        jetty_factory,
        sotty_factory,
        watty_factory,
        api_client,
        region_de,
        country_factory,
        django_assert_max_num_queries,
    ):
        region_de.countries.add(country_factory(germany=True))
        user = user_factory(
            profile__language=LanguageEnum.EN, profile__region=region_de
        )
        jetty_factory(owner=user, furniture_status=FurnitureStatusEnum.SAVED)
        sotty_factory(owner=user, furniture_status=FurnitureStatusEnum.SAVED)
        watty_factory(owner=user, furniture_status=FurnitureStatusEnum.SAVED)
        api_client.force_authenticate(user)

        with django_assert_max_num_queries(27):
            api_client.get(reverse('wishlist-list'))


@pytest.mark.django_db
class TestWishlistDetailApiView:
    @pytest.mark.parametrize(
        ('url_name', 'furniture_factory'),
        [
            ('jetty-wishlist-detail', 'jetty_factory'),
            ('sotty-wishlist-detail', 'sotty_factory'),
            ('watty-wishlist-detail', 'watty_factory'),
        ],
    )
    def test_wishlist_details_get_response_200(
        self, api_client, user, url_name, furniture_factory, request
    ):
        furniture_factory = request.getfixturevalue(furniture_factory)
        furniture = furniture_factory(owner=user)
        url = reverse(url_name, kwargs={'pk': furniture.id})
        api_client.force_authenticate(furniture.owner)

        response = api_client.get(url)

        assert response.status_code == status.HTTP_200_OK
        response_json = response.json()
        assert response_json['id'] == furniture.id
        assert response_json['category'] == furniture.furniture_category
        assert response_json['shelf_type'] == furniture.shelf_type
        assert response_json.keys() == {
            'category',
            'id',
            'omnibus_price',
            'preview',
            'promotion',
            'region_price',
            'region_price_with_discount',
            'shelf_type',
        }

    def test_performance_max_num_queries(
        self, api_client, user, jetty_factory, django_assert_max_num_queries
    ):
        jetty = jetty_factory(owner=user)
        url = reverse('jetty-wishlist-detail', kwargs={'pk': jetty.id})
        api_client.force_authenticate(jetty.owner)

        with django_assert_max_num_queries(20):
            api_client.get(url)


@pytest.mark.django_db
@pytest.mark.parametrize(
    'shelf_category',
    (
        FurnitureCategory.CHEST,
        FurnitureCategory.TV_STAND,
        FurnitureCategory.SIDEBOARD,
        FurnitureCategory.BEDSIDE_TABLE,
    ),
)
def test_add_to_cart_watty_low_categories(
    api_client,
    shelf_category,
):
    fixtures_path = Path(f'{settings.BASE_DIR}/gallery/tests/low_tone_fixture.json')
    with open(fixtures_path, 'r') as new_expression_fixture:
        data = json.load(new_expression_fixture)
    data['shelf_category'] = shelf_category
    url = reverse('watty-add-to-cart')

    response = api_client.post(url, data, format='json')
    assert response.status_code == status.HTTP_201_CREATED

    created_shelf = Watty.objects.last()
    assert created_shelf.shelf_category == shelf_category


@pytest.mark.django_db
def test_add_to_cart_sample_box_many(
    api_client,
    sample_box_variant_factory,
    user,
):
    sample_box_variant_1 = sample_box_variant_factory()
    sample_box_variant_2 = sample_box_variant_factory()
    data = {
        'items': [
            {'box_variant': sample_box_variant_1.variant_type},
            {'box_variant': sample_box_variant_1.variant_type},
            {'box_variant': sample_box_variant_2.variant_type},
        ]
    }
    expected_variant_types = sorted(
        [variant['box_variant'] for variant in data['items']]
    )
    url = reverse('samplebox-add-to-cart')
    api_client.force_authenticate(user)

    response = api_client.post(url, data, format='json')

    assert response.status_code == status.HTTP_201_CREATED
    cart = CartService.get_cart(user)
    variant_types_in_cart = sorted(
        item.sellable_item.box_variant.variant_type for item in cart.items.all()
    )
    assert variant_types_in_cart == expected_variant_types


@pytest.mark.django_db
def test_add_to_cart_sample_box_one(
    api_client,
    sample_box_variant_factory,
    user,
):
    sample_box_variant_1 = sample_box_variant_factory()
    data = {'items': [{'box_variant': sample_box_variant_1.variant_type}]}
    url = reverse('samplebox-add-to-cart')
    api_client.force_authenticate(user)

    response = api_client.post(url, data, format='json')

    assert response.status_code == status.HTTP_201_CREATED
    sample_in_cart = CartService.get_cart(user).items.first().sellable_item
    assert sample_in_cart.box_variant.variant_type == sample_box_variant_1.variant_type


@pytest.mark.django_db
def test_add_to_cart_sample_box_sofa_and_storage_check_prices(
    api_client,
    sample_box_variant_factory,
    sample_price_settings_factory,
    user,
):
    sample_price_settings_factory(
        storage_sample_price=1,
        storage_sample_promo_active=False,
        sofa_sample_price=2,
        sofa_sample_promo_active=False,
    )
    storage_sample_variant = sample_box_variant_factory(wooden=True)
    sofa_sample_variant = sample_box_variant_factory(sofa=True)
    data = {
        'items': [
            {'box_variant': storage_sample_variant.variant_type},
            {'box_variant': sofa_sample_variant.variant_type},
        ]
    }
    url = reverse('samplebox-add-to-cart')
    api_client.force_authenticate(user)

    response = api_client.post(url, data, format='json')

    assert response.status_code == status.HTTP_201_CREATED
    sofa_sample = SampleBox.objects.get(owner=user, box_variant=sofa_sample_variant)
    storage_sample = SampleBox.objects.get(
        owner=user, box_variant=storage_sample_variant
    )
    assert storage_sample.price == 1
    assert sofa_sample.price == 2


@pytest.mark.skip
@pytest.mark.django_db
class TestInfluencersLPFurnitureListView:
    influ_lp_url = reverse('influencers-lp-api')

    def test_successful_response_with_jetty(self, api_client, jetty):
        response = api_client.get(
            self.influ_lp_url, data={'jetty_ids': [jetty.id]}, format='json'
        )
        assert response.status_code == status.HTTP_200_OK
        assert response.data[0]['id'] == jetty.id

    def test_successful_response_with_watty(self, api_client, watty):
        response = api_client.get(
            self.influ_lp_url, data={'watty_ids': [watty.id]}, format='json'
        )
        assert response.status_code == status.HTTP_200_OK
        assert response.data[0]['id'] == watty.id

    def test_successful_response_with_both(self, api_client, jetty, watty):
        response = api_client.get(
            self.influ_lp_url,
            data={'jetty_ids': [jetty.id], 'watty_ids': [watty.id]},
            format='json',
        )
        assert response.status_code == status.HTTP_200_OK
        assert len(response.data) == 2

    def test_empty_response_when_no_ids_provided(self, api_client):
        response = api_client.get(self.influ_lp_url, data={}, format='json')
        assert response.status_code == status.HTTP_200_OK
        assert response.data == []

    def test_empty_response_when_ids_do_not_exist(
        self, api_client, jetty_factory, watty_factory
    ):
        jetty = jetty_factory()
        jetty_id = jetty.id
        watty = watty_factory()
        watty_id = watty.id
        jetty.delete()
        watty.delete()
        response = api_client.get(
            self.influ_lp_url,
            data={'jetty_ids': [jetty_id], 'watty_ids': [watty_id]},
            format='json',
        )
        assert response.status_code == status.HTTP_200_OK
        assert response.data == []

    def test_performance(
        self,
        api_client,
        jetty,
        watty,
        django_assert_max_num_queries,
    ):
        with django_assert_max_num_queries(20):
            response = api_client.get(
                self.influ_lp_url,
                data={'jetty_ids': [jetty.id], 'watty_ids': [watty.id]},
                format='json',
            )
            assert response.status_code == status.HTTP_200_OK


class TestPdpViewCases:
    def case_with_promotion(
        self,
        promotion_factory,
        promotion_config_factory,
    ):
        promotion = promotion_factory(
            strikethrough_pricing=True,
            promo_code__is_percentage=True,
        )
        promotion_config_factory(
            promotion=promotion,
        )
        return promotion.promo_code.value

    def case_without_promotion(self):
        return None


class TestPdpView:
    @parametrize_with_cases('expected_discount', cases=TestPdpViewCases)
    def test_get_sotty_pdp(self, expected_discount, api_client, sotty_factory):
        sotty = sotty_factory(
            weight=100,
        )
        url = reverse('sotty-pdp', kwargs={'pk': sotty.pk})
        response = api_client.get(url)

        assert response.status_code == status.HTTP_200_OK
        assert response.data['discount_value'] == expected_discount
        assert response.data['seating_depth'] == sotty.get_seating_depth()
        assert response.data['armrest_height'] == sotty.get_armrest_height()
        assert response.data['seat_height'] == sotty.get_seat_height()
        assert response.data['weight'] == sotty.weight

    def test_get_sotty_pdp_eco_tax(self, api_client, sotty_factory, user, region_fr):
        sotty = sotty_factory(weight=100)

        user.profile.region = region_fr
        user.profile.save(update_fields=['region'])

        api_client.force_authenticate(user)

        url = reverse('sotty-pdp', kwargs={'pk': sotty.pk})
        response = api_client.get(url)

        assert response.status_code == status.HTTP_200_OK
        assert response.data['eco_tax'] == get_recycle_tax_value(sotty.weight)

    def test_get_sotty_pdp_omnibus_price(
        self,
        user,
        api_client,
        sotty_factory,
        promotion_factory,
        promotion_config_factory,
        voucher_factory,
    ):
        sotty = sotty_factory(weight=100)
        url = reverse('sotty-pdp', kwargs={'pk': sotty.pk})
        promotion = promotion_factory(
            strikethrough_pricing=True,
            promo_code=voucher_factory(is_percentage=True),
        )
        promotion_config_factory(promotion=promotion)

        expected_omnibus_price = Decimal('100.00')
        with mock.patch(
            'pricing_v3.omnibus.OmnibusCalculator._get_base_price',
            return_value=expected_omnibus_price,
        ):
            with mock.patch(
                'pricing_v3.history.current.PricingCalculator.get_price'
            ) as mock_get_price:
                mock_get_price.side_effect = NotImplementedError()
                response = api_client.get(url)

        assert response.status_code == status.HTTP_200_OK
        assert response.data['omnibus_price'] == expected_omnibus_price

    def test_sotty_corduroy_material_overwrite_in_uk_pdp(
        self, user, api_client, sotty_factory, region_uk
    ):
        sotty = sotty_factory(
            materials=[Sofa01Color.CORDUROY_ECRU],
        )
        user.profile.region = region_uk
        user.profile.save(update_fields=['region'])
        api_client.force_authenticate(user)
        url = reverse('sotty-pdp', kwargs={'pk': sotty.pk})
        response = api_client.get(url)

        assert response.status_code == status.HTTP_200_OK
        assert response.data['material'] == Sofa01Color.REWOOL2_BROWN.value


@pytest.mark.django_db
class TestProductPageSottyCoverAPIView:
    @patch.object(
        SottySingleModuleRepository,
        'get_module_type',
        return_value=SottyModuleType.FOOTREST,
        autospec=True,
    )
    @patch.object(
        SottySingleModuleRepository,
        'get_module_type_siblings',
        return_value=[],
        autospec=True,
    )
    @patch.object(
        SottySingleModuleRepository,
        'get_attributes_map',
        return_value={
            'material': {'0': 123, '1': 124, '2': 125},
            'width': {'1000': 999, '1125': 998},
            'depth': {'750': 456, '875': 457, '1000': 458, '1125': 459},
        },
        autospec=True,
    )
    def test_fetch_single_module_cover(
        self, mock_attributes_map, __, ___, api_client, sotty_factory, user
    ):
        cover_sotty = sotty_factory(
            covers_only=True,
            preset=True,
            shelf_category=FurnitureCategory.COVER,
            materials=[Sofa01Color.REWOOL2_BROWN],
            width=1_000,
            depth=1125,
            configurator_params={
                'layout': [{'modules': [{'type': SottyModuleType.FOOTREST}]}]
            },
        )
        url = reverse('sotty-single-module-pdp', kwargs={'pk': cover_sotty.pk})
        api_client.force_authenticate(user)
        response = api_client.get(url)

        assert response.status_code == status.HTTP_200_OK
        response_json = response.json()
        assert response_json['furnitureCategory'] == FurnitureCategory.COVER
        assert response_json['attributes'] == mock_attributes_map.return_value
        assert response_json['activeAttributes'] == {
            'material': cover_sotty.material,
            'width': cover_sotty.width,
            'depth': cover_sotty.depth,
        }
        assert response_json['moduleName'] == 'footrest'
