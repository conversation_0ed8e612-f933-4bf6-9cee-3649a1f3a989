from django.db import models
from django.db.models import (
    Func,
    IntegerField,
)


class JsonbArrayLength(Func):
    function = 'jsonb_array_length'
    output_field = IntegerField()


class JsonbExtractPath(Func):
    """
    jsonb_extract_path(target jsonb, VARIADIC text[])
    so we can pass the path pieces as extra arguments.
    """

    function = 'jsonb_extract_path'
    output_field = models.JSONField()
