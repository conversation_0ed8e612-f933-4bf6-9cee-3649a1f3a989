from django.urls import (
    path,
    re_path,
    reverse_lazy,
)
from django.utils.translation import gettext_lazy
from django.views.generic import TemplateView
from django.views.generic.base import RedirectView

from .views.account import (
    FrontChangePassword,
    FrontOrderCancel,
)
from .views.product_list import ProductListRedirectView
from .views.redirect import FrontProductTypeRedirectPage
from .views.static_pages import ReverseOnlyView

# These URLs are used only for reverse lookup within Django templates.
# TODO: Remove after all templates are moved to Nuxt.
reverse_urls = [
    path('referral-program/', ReverseOnlyView.as_view(), name='front-refer'),
    path('journal/', ReverseOnlyView.as_view(), name='front-journal'),
    path('faq/', ReverseOnlyView.as_view(), name='front-faq'),
    path('library/', ReverseOnlyView.as_view(), name='front-library'),
    path('register_or_login/', ReverseOnlyView.as_view(), name='front-register-login'),
    path('account/', ReverseOnlyView.as_view(), name='front-account'),
    path('privacy_policy/', ReverseOnlyView.as_view(), name='front-privacy-policy'),
    path('press/', ReverseOnlyView.as_view(), name='front-press'),
    path('get-app/', ReverseOnlyView.as_view(), name='front-get-app'),
    path('shipping/', ReverseOnlyView.as_view(), name='front-shipping'),
    path('terms/', ReverseOnlyView.as_view(), name='front-terms'),
    path('', ReverseOnlyView.as_view(), name='front-homepage'),
    re_path(
        gettext_lazy(r'^front-tylko-for-business$'),
        ReverseOnlyView.as_view(),
        name='front-tylko-for-business',
    ),
    re_path(
        gettext_lazy(r'^front-rooms$'),
        ReverseOnlyView.as_view(),
        name='front-rooms',
    ),
    re_path(
        gettext_lazy(r'^front-rooms$'),
        ReverseOnlyView.as_view(),
        name='front-rooms',
    ),
    re_path(
        gettext_lazy(r'^our-mission/'),
        ReverseOnlyView.as_view(),
        name='front-story-static',
    ),
    re_path(
        gettext_lazy(r'^product-lines/storage'),
        ReverseOnlyView.as_view(),
        name='front-compare-shelves',
    ),
    re_path(
        gettext_lazy(r'^product-lines/wardrobes'),
        ReverseOnlyView.as_view(),
        name='front-compare-wardrobes',
    ),
    re_path(
        gettext_lazy(r'^product-lines/'),
        ReverseOnlyView.as_view(),
        name='front-product-lines',
    ),
    re_path(
        gettext_lazy(r'^gallery/'),
        ReverseOnlyView.as_view(),
        name='front-gallery',
    ),
    re_path(
        gettext_lazy(r'^material-samples/'),
        ReverseOnlyView.as_view(),
        name='front-samples',
    ),
    re_path(
        gettext_lazy(r'^contact/'),
        ReverseOnlyView.as_view(),
        name='front-contact',
    ),
]

# These URLs are used only for redirecting old url to new one in Nuxt.
# TODO: Remove after we decide not to support old urls.
redirect_urls = [
    re_path(
        gettext_lazy(r'^sofa/(?P<furniture_category>[a-z\-_]+)/(?P<pk>[0-9]+),s,/'),
        ReverseOnlyView.as_view(),
        name='front-product-sotty',
    ),
    re_path(
        gettext_lazy(r'^configure/(?P<pk>[0-9]+),s,/'),
        ReverseOnlyView.as_view(),
        name='front-product-sotty-configurator',
    ),
    re_path(
        'sofa/cover/(?P<pk>[0-9]+)/',
        ReverseOnlyView.as_view(),
        name='front-product-sotty-cover',
    ),
    re_path(
        gettext_lazy(
            r'^furniture/(?P<furniture_category>[a-z\-_]+)/(?P<pk>[0-9]+),w,/'
        ),
        ReverseOnlyView.as_view(),
        name='front-product-watty',
    ),
    re_path(
        gettext_lazy(
            r'^furniture/(?P<furniture_category>[a-z\-_]+)/(?P<pk>[0-9]+),j,/'
        ),
        ReverseOnlyView.as_view(),
        name='front-product-shelf',
    ),
    path(
        'product/<slug:product_type>/<int:pk>/',
        FrontProductTypeRedirectPage.as_view(),
        name='front-product',
    ),
    path(
        'product/<slug:product_type>/',
        FrontProductTypeRedirectPage.as_view(),
        name='front-product-type-redirect',
    ),
    re_path(
        gettext_lazy(r'^shelves/$'),
        ProductListRedirectView.as_view(),
        name='front-products-list-shelf',
    ),
    re_path(
        gettext_lazy('^products/$'),
        ProductListRedirectView.as_view(),
        name='front-products-list-nuxt-redirect',
    ),
    re_path(
        gettext_lazy('^products/(?P<furniture_category>.+)/$'),
        ProductListRedirectView.as_view(),
        name='front-products-list-nuxt-redirect',
    ),
    re_path(
        gettext_lazy(r'^shelves/(?P<furniture_category>.+)/$'),
        ProductListRedirectView.as_view(),
        name='front-products-list-shelf',
    ),
    re_path(
        gettext_lazy(r'^shelf/'),
        FrontProductTypeRedirectPage.as_view(),
        name='front-product-shelf',
    ),
    re_path(
        gettext_lazy(r'^inspiration-gallery/'),
        RedirectView.as_view(url=reverse_lazy('front-samples'), permanent=True),
        name='front-inspiration-gallery',
    ),
    re_path(
        gettext_lazy(r'^visit-our-showroom/'),
        RedirectView.as_view(url=reverse_lazy('front-homepage'), permanent=True),
        name='front-showroom',
    ),
    re_path(
        r'^order/(?P<pk>[0-9]+)/cancel/',
        FrontOrderCancel.as_view(permanent=False),
        name='front-order-cancel',
    ),
]

# TODO: move to Nuxt.
django_templates_urls = [
    path(
        'change-password/<str:token>/',
        FrontChangePassword.as_view(),
        name='front-changepassword-token',
    ),
    path(
        'test/empty-base-without-content/',
        TemplateView.as_view(template_name='front/test/empty-base.html'),
        name='empty-base',
    ),
    re_path(
        gettext_lazy(r'^front-tylko-for-business-refer$'),
        TemplateView.as_view(template_name='front/refer_mention_me_b2b.html'),
        name='front-tylko-for-business-refer',
    ),
]

urlpatterns = reverse_urls + redirect_urls + django_templates_urls
