from django.contrib import admin
from django.contrib.admin import DateFieldListFilter
from django.core.cache import cache
from django.db.models import (
    OuterRef,
    Subquery,
)

from custom.admin_filters import InputFilter
from custom.models import Countries
from invoice.models import (
    DOMESTIC_RELEASE_DATETIME,
    Invoice,
)
from orders.models import PaidOrders


class OrderCountryFilter(admin.SimpleListFilter):
    title = 'Country'
    parameter_name = 'country'

    def lookups(self, request, model_admin):
        key = 'countries_for_admin'
        countries = cache.get(key)
        if not countries:
            countries = (
                PaidOrders.objects.distinct('country')
                .values('country')
                .order_by('country')
            )
            cache.set(key, countries, 86400)
        return [[country['country'], country['country']] for country in countries]

    def queryset(self, request, queryset):
        if self.value():
            return queryset.filter(order__country=self.value())
        return queryset


class SentInvoiceAtWithCustomNoDateDateFieldListFilter(DateFieldListFilter):
    NO_DATE_LOOKUP = {'sent_invoice_at__isnull': True}

    def queryset(self, request, queryset):
        if self.used_parameters == self.NO_DATE_LOOKUP:
            related_invoices = Invoice.objects.filter(
                order=OuterRef('order__id')
            ).order_by('id')

            queryset = queryset.annotate(
                first_issued_at=Subquery(related_invoices.values('issued_at')[:1]),
            )
            return (
                super()
                .queryset(request, queryset)
                .exclude(
                    sent_invoice_at__isnull=True,
                    order__country=Countries.united_kingdom.name,
                    first_issued_at__gte=DOMESTIC_RELEASE_DATETIME,
                )
            )
        return super().queryset(request, queryset)


class InvoicePrettyIdFilter(InputFilter):
    parameter_name = 'pretty_id'
    title = 'Pretty IDs (sep. by space)'

    def queryset(self, request, queryset):
        if not self.value():
            return queryset

        return queryset.filter(pretty_id__in=self.value().split())
