import logging

from decimal import Decimal

from django.conf import settings
from django.contrib.auth.models import User
from django.contrib.postgres.fields import ArrayField
from django.contrib.postgres.indexes import GinIndex
from django.db import (
    models,
    transaction,
)
from django.db.models import (
    F,
    OuterRef,
    Q,
    Subquery,
)
from django.db.models.functions import Upper
from django.utils import timezone

from custom.models import (
    ExchangeRate,
    Timestampable,
)
from customer_service.enums import (
    AbortRequestStatus,
    CSActivity,
    CSCorrectionRequestStatus,
    CSCorrectionRequestType,
    CSUnsuccessfulPaymentsStatus,
    KlarnaPriceChangeType,
    KlarnaSource,
)
from customer_service.managers import (
    CSOrderManager,
    CSUserProfileManager,
    OrderItemAbortRequestQuerySet,
    ReturningCustomerManager,
)
from invoice.choices import VatType
from invoice.enums import InvoiceItemTag
from invoice.models import InvoiceItem
from mailing.models import Customer
from orders.enums import OrderStatus
from pricing_v3.services.price_calculators import (
    OrderPriceCalculatorForPriceUpdatedAtPricing,
)
from user_profile.choices import UserType
from vouchers.utils import get_or_create_klarna_absolute_discount_voucher

logger = logging.getLogger('cstm')


class CSActivityLog(models.Model):
    activity_context = models.CharField(blank=True, max_length=1024, null=True)
    activity_type = models.PositiveSmallIntegerField(choices=CSActivity.choices)
    timestamp = models.DateTimeField(auto_now_add=True, db_index=True)
    user = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
    )


class CSCorrectionRequest(models.Model):
    correction_amount_gross = models.DecimalField(
        max_digits=12, decimal_places=2, null=True
    )
    correction_context = models.TextField(blank=True, null=True)
    correction_vat = models.PositiveSmallIntegerField(
        help_text='To change vat type',
        blank=True,
        choices=VatType.choices,
        null=True,
    )
    invoice = models.ForeignKey(
        'invoice.Invoice',
        on_delete=models.CASCADE,
    )
    correction_invoice = models.ForeignKey(
        'invoice.Invoice',
        on_delete=models.SET_NULL,
        blank=True,
        null=True,
        related_name='correction_correction_requests',
    )
    issuer = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        related_name='issued_correction_requests',
        on_delete=models.CASCADE,
    )
    reviewer = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        blank=True,
        null=True,
        related_name='reviewed_correction_requests',
        on_delete=models.CASCADE,
    )
    status = models.PositiveSmallIntegerField(
        choices=CSCorrectionRequestStatus.choices,
        default=CSCorrectionRequestStatus.STATUS_NEW,
    )
    type_cs = models.PositiveSmallIntegerField(
        choices=CSCorrectionRequestType.choices,
        default=CSCorrectionRequestType.TYPE_AMOUNT_VAT,
    )

    added_invoice_items = models.ManyToManyField(
        InvoiceItem, blank=True, related_name='added_items_correction_requests'
    )
    deleted_invoice_items = models.ManyToManyField(InvoiceItem, blank=True)
    cs_correction_request = models.OneToOneField(
        'CSCorrectionAddressRequest',
        blank=True,
        null=True,
        on_delete=models.SET_NULL,
    )
    tag = models.IntegerField(
        choices=InvoiceItemTag.choices(),
        default=None,
        null=True,
        blank=True,
    )
    discount_tag = models.CharField(
        max_length=60,
        default='',
        blank=True,
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    complaint = models.OneToOneField(
        'complaints.Complaint',
        related_name='correction_request',
        on_delete=models.CASCADE,
        null=True,
        blank=True,
    )

    def __str__(self):
        return (
            f'CSCorrectionRequest (ID: {self.id}) - '
            f'Gross Amount: {self.correction_amount_gross} - '
            f'Type: {self.get_type_cs_display()} - '
            f'Status: {self.get_status_display()}'
        )

    def is_processed(self):
        correction_request_statuses = {
            CSCorrectionRequestStatus.STATUS_ACCEPTED,
            CSCorrectionRequestStatus.STATUS_REJECTED,
        }
        return self.status in correction_request_statuses

    @property
    def tag_to_reason(self):
        if self.tag:
            return InvoiceItemTag.invoice_tag_to_invoice_reason_mapper(self.tag)
        return 'No tag'

    def get_estimated_euro_price(self):
        if self.invoice.currency_symbol == '€':
            return self.correction_amount_gross

        local_price_brutto = self.correction_amount_gross
        exrate = ExchangeRate.get_safe_exchange(
            self.created_at.year,
            self.created_at.month,
            self.created_at.day,
        )
        currency_code = self.invoice.get_currency_code()
        if currency_code == 'PLN':
            return round(float(local_price_brutto) / exrate['EUR'], 2)
        return round(
            (float(exrate[currency_code]) * float(local_price_brutto))
            / float(exrate['EUR']),
            2,
        )

    def address_diff(self):
        if self.type_cs != CSCorrectionRequestType.TYPE_ADDRESS.value:
            return 'No address changes'
        fields = [f.name for f in CSCorrectionAddressRequest._meta.fields]
        fields.remove('id')
        diff = {}
        for field in fields:
            if getattr(self.invoice.order, field) != getattr(
                self.cs_correction_request, field
            ):
                diff[field] = (
                    getattr(self.invoice.order, field),
                    getattr(self.cs_correction_request, field),
                )
        info = 'Diff between address</br>'
        for idx, d in enumerate(diff, start=1):
            info += '{} before: <b>{}</b> after: <b>{}</b> '.format(
                d.capitalize(),
                '' if not diff[d][0] else diff[d][0],
                '' if not diff[d][1] else diff[d][1],
            )
            if not idx % 1:
                info += '</br>'
        return info


class CSCorrectionAddressRequest(models.Model):
    first_name = models.CharField('first name', max_length=256, blank=True, default='')
    last_name = models.CharField('last name', max_length=256, blank=True, default='')
    street_address_1 = models.CharField(
        'street address 1', max_length=256, blank=True, default=''
    )
    street_address_2 = models.CharField(
        'street address 2', max_length=256, blank=True, default=''
    )
    company_name = models.CharField(
        'company name', max_length=256, blank=True, default=''
    )
    city = models.CharField('city', max_length=256, blank=True, default='')
    postal_code = models.CharField('postal code', max_length=20, blank=True, default='')
    country = models.CharField(
        'country', max_length=50, blank=True, default='', db_index=True
    )
    vat = models.CharField('tax id (vat)', max_length=256, blank=True, default='')


class DateAddMinus(models.Func):
    def __init__(self, *expressions, **extra):
        self.sign = extra.pop('sign')
        self.arg_joiner = " {} INTERVAL '".format(self.sign)
        super(DateAddMinus, self).__init__(*expressions, **extra)

    template = "(%(expressions)s days')"
    output_field = models.DateTimeField()


class CSUnsuccessfulPayments(models.Model):
    order = models.OneToOneField('orders.Order', on_delete=models.CASCADE)
    status = models.IntegerField(
        choices=CSUnsuccessfulPaymentsStatus.choices,
        default=CSUnsuccessfulPaymentsStatus.NEW,
    )
    note = models.TextField(null=True, blank=True)
    user = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        limit_choices_to={
            'profile__user_type__in': [
                UserType.CUSTOMER_SERVICE,
                UserType.STAFF,
            ]
        },
        on_delete=models.CASCADE,
    )
    timestamp = models.DateTimeField(auto_now_add=True, db_index=True)

    @staticmethod
    def find_orders():
        from datetime import (
            date,
            datetime,
            timedelta,
        )

        from orders.models import Order

        paid = Order.objects.filter(
            Q(owner=OuterRef('owner')) | Q(email=OuterRef('email'))
        ).filter(
            total_price__gte=OuterRef('total_price_gte'),
            total_price__lte=OuterRef('total_price_lte'),
            updated_at__gte=OuterRef('updated_at_gte'),
            updated_at__lte=OuterRef('updated_at_lte'),
            status__in=[
                OrderStatus.IN_PRODUCTION,
                OrderStatus.SHIPPED,
                OrderStatus.DELIVERED,
                OrderStatus.TO_BE_SHIPPED,
            ],
        )

        orders = (
            Order.objects.filter(
                Q(
                    Q(status=OrderStatus.PAYMENT_PENDING)
                    | Q(status=OrderStatus.PAYMENT_FAILED)
                )
                & Q(
                    updated_at__range=(
                        date.today() - timedelta(days=7 * 5),
                        datetime.today() - timedelta(minutes=10),
                    )
                )
                & Q(csunsuccessfulpayments__isnull=True)
            )
            .annotate(
                updated_at_gte=DateAddMinus('updated_at', 5, sign='-'),
                updated_at_lte=DateAddMinus('updated_at', 5, sign='+'),
                total_price_gte=F('total_price') - 50,
                total_price_lte=F('total_price') + 50,
            )
            .annotate(similar_order=Subquery(paid.values('pk')[:1]))
        )

        for order in orders:
            if order.similar_order is None:
                CSUnsuccessfulPayments(order=order, user=User.objects.get(pk=1)).save()

    @property
    def total_price(self):
        return self.order.total_price

    @property
    def total_price_net(self):
        return self.order.total_price_net

    @property
    def updated_at(self):
        return self.order.updated_at

    @property
    def email(self):
        return self.order.email

    @property
    def phone(self):
        return self.order.phone

    @property
    def language(self):
        return self.order.owner.profile.language

    @property
    def source(self):
        return self.order.chosen_payment_method

    @staticmethod
    def clean_already_payed_orders():
        from orders.models import Order

        orders = Order.objects.filter(
            csunsuccessfulpayments__status=CSUnsuccessfulPaymentsStatus.NEW,
            status__in=[
                OrderStatus.IN_PRODUCTION,
                OrderStatus.SHIPPED,
                OrderStatus.DELIVERED,
                OrderStatus.TO_BE_SHIPPED,
            ],
        )
        for o in orders:
            cup = o.csunsuccessfulpayments
            cup.status = CSUnsuccessfulPaymentsStatus.PAYED
            cup.user = User.objects.get(pk=1)
            cup.save()


class ReturningCustomer(Customer):
    objects = ReturningCustomerManager()

    class Meta:
        proxy = True


class CSOrder(models.Model):
    ORDER_REQUIRED_FIELD_NAMES = [
        'first_name',
        'invoice_first_name',
        'last_name',
        'invoice_last_name',
        'company_name',
        'email',
        'invoice_email',
        'phone',
        'city',
        'invoice_city',
        'street_address_1',
        'invoice_street_address_1',
        'street_address_2',
        'invoice_street_address_2',
        'promo_text',
    ]

    order_pretty_id = models.CharField(max_length=50, blank=True)

    status = models.IntegerField(
        choices=OrderStatus.choices, default=OrderStatus.DRAFT, db_index=True
    )

    first_name = models.CharField('first name', max_length=256, blank=True)
    invoice_first_name = models.CharField(
        'invoice first name', max_length=256, blank=True
    )

    last_name = models.CharField('last name', max_length=256, blank=True)
    invoice_last_name = models.CharField(
        'invoice last name', max_length=256, blank=True
    )
    company_name = models.CharField('company name', max_length=256, blank=True)
    invoice_company_name = models.CharField('company name', max_length=256, blank=True)

    email = models.EmailField(blank=True)
    invoice_email = models.CharField('invoice email', max_length=256)
    owner_email = models.EmailField(blank=True)

    phone = models.CharField('phone number', max_length=30, blank=True, db_index=True)
    city = models.CharField('city', max_length=256, blank=True)
    invoice_city = models.CharField('invoice city', max_length=256, blank=True)

    street_address_1 = models.CharField('street address 1', max_length=256, blank=True)
    invoice_street_address_1 = models.CharField(
        'invoice street address 1', max_length=256, blank=True
    )

    street_address_2 = models.CharField('street address 2', max_length=256, blank=True)
    invoice_street_address_2 = models.CharField(
        'invoice street address 2', max_length=256, blank=True
    )

    promo_text = models.CharField('Promo code text', max_length=400, blank=True)
    product_ids = ArrayField(models.IntegerField())
    owner_username = models.CharField(max_length=150)

    objects = CSOrderManager()

    class Meta:
        indexes = [
            models.Index(Upper('first_name'), name='upper_csorder_first_name'),
            models.Index(
                Upper('invoice_first_name'), name='upper_cs_invoice_first_name'
            ),
            models.Index(Upper('last_name'), name='upper_csorder_last_name'),
            models.Index(Upper('invoice_last_name'), name='upper_cs_invoice_last_name'),
            models.Index(Upper('email'), name='upper_csorder_email'),
            models.Index(Upper('invoice_email'), name='upper_csorder_invoice_email'),
            models.Index(Upper('phone'), name='upper_csorder_phone'),
            models.Index(Upper('promo_text'), name='upper_promo_text'),
            GinIndex(fields=('product_ids',), name='gin_product_ids'),
        ]

    @classmethod
    def should_update_or_create_from_order(cls, order):
        return any(
            [
                getattr(order, field_name)
                for field_name in cls.ORDER_REQUIRED_FIELD_NAMES
            ]
        )


class CSUserProfile(models.Model):
    USER_PROFILE_REQUIRED_FIELD_NAMES = [
        'first_name',
        'last_name',
        'email',
        'phone',
        'city',
        'invoice_city',
        'street_address_1',
        'invoice_street_address_1',
        'street_address_2',
        'invoice_street_address_2',
    ]

    user_type = models.IntegerField(choices=UserType.choices, default=UserType.CUSTOMER)
    user_id = models.IntegerField()
    email = models.EmailField(blank=True)

    first_name = models.CharField('first name', max_length=256, blank=True)
    last_name = models.CharField('last name', max_length=256, blank=True)
    user_email = models.EmailField(blank=True)
    user_username = models.CharField(max_length=150)

    company_name = models.CharField('company name', max_length=256, blank=True)

    phone = models.CharField('phone number', max_length=30, blank=True)
    city = models.CharField('city', max_length=256, blank=True)
    invoice_city = models.CharField('invoice city', max_length=256, blank=True)
    street_address_1 = models.CharField('street address 1', max_length=256, blank=True)
    invoice_street_address_1 = models.CharField(
        'street address 1', max_length=256, blank=True
    )

    street_address_2 = models.CharField('street address 2', max_length=256, blank=True)
    invoice_street_address_2 = models.CharField(
        'street address 2', max_length=256, blank=True
    )

    objects = CSUserProfileManager()

    class Meta:
        indexes = [
            models.Index(Upper('first_name'), name='upper_csuserprofile_first_name'),
            models.Index(Upper('last_name'), name='upper_csuserprofile_last_name'),
            models.Index(Upper('email'), name='upper_csuserprofile_email'),
            models.Index(Upper('user_email'), name='upper_csuserprofile_user_email'),
            models.Index(Upper('user_username'), name='upper_csuserprofile_username'),
            models.Index(Upper('phone'), name='upper_csuserprofile_phone'),
        ]

    @classmethod
    def should_update_or_create_from_user_profile(cls, user_profile):
        return any(
            [
                getattr(user_profile, field_name)
                for field_name in cls.USER_PROFILE_REQUIRED_FIELD_NAMES
            ]
            + [user_profile.user.email]
        )


class KlarnaAdjustment(Timestampable):
    invoice = models.ForeignKey(
        'invoice.Invoice',
        related_name='adjustments',
        on_delete=models.SET_NULL,
        null=True,
    )
    change_type = models.PositiveSmallIntegerField(
        choices=KlarnaPriceChangeType.choices,
    )
    amount = models.DecimalField(max_digits=12, decimal_places=2)
    current_price = models.DecimalField(
        max_digits=12,
        decimal_places=2,
        default=Decimal('0.00'),
    )
    conversation_link = models.CharField(
        verbose_name='Dixa link', max_length=256, blank=True
    )
    reason = models.TextField(blank=True)
    source = models.PositiveSmallIntegerField(choices=KlarnaSource.choices)
    finished_at = models.DateTimeField(
        blank=True,
        null=True,
        editable=False,
    )

    def __str__(self):
        return (
            f'KlarnaAdjustment (ID: {self.id}) - '
            f'Gross Amount: {self.amount} - '
            f'Type: {self.get_change_type_display()}'
        )

    @property
    def currency_symbol(self):
        return self.invoice.order.region.currency.code

    @transaction.atomic
    def accept_customer_service_source(self) -> None:
        order = self.invoice.order

        if self.change_type == KlarnaPriceChangeType.DISCOUNT:
            target_voucher_value = order.region_promo_amount + self.amount

            target_voucher = get_or_create_klarna_absolute_discount_voucher(
                order.region, target_voucher_value
            )
            order.set_used_promo_with_history(target_voucher)

            calculator = OrderPriceCalculatorForPriceUpdatedAtPricing(order)
            calculator.calculate(change_promo_quantity=True, recalculate_items=False)
            self.invoice.recreate_items()

        self.finished_at = timezone.now()
        self.save(update_fields=['finished_at'])

    @transaction.atomic
    def accept_add_extra_discount_source(self) -> None:
        self.invoice.recreate_items()
        self.finished_at = timezone.now()
        self.save(update_fields=['finished_at'])


class OrderItemAbortRequest(models.Model):
    order = models.ForeignKey(
        'orders.Order',
        on_delete=models.CASCADE,
        related_name='abort_requests',
    )
    status = models.IntegerField(
        choices=AbortRequestStatus.choices,
        default=AbortRequestStatus.PENDING,
    )
    order_items_with_quantity = models.JSONField(default=list, blank=True)
    requested_at = models.DateTimeField(auto_now_add=True)
    executed_at = models.DateTimeField(null=True, blank=True)
    exception_messages = models.CharField(max_length=256, blank=True)
    requested_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        null=True,
        blank=True,
        on_delete=models.SET_NULL,
    )

    objects = OrderItemAbortRequestQuerySet.as_manager()

    class Meta:
        ordering = ['-requested_at']

    def update_status_to_cancelled(self) -> None:
        if self.status == AbortRequestStatus.PENDING:
            self.status = AbortRequestStatus.CANCELLED
            self.save(update_fields=['status'])

    def update_status_to_done(self) -> None:
        if self.status == AbortRequestStatus.PENDING:
            self.status = AbortRequestStatus.DONE
            self.executed_at = timezone.now()
            self.save(update_fields=['status', 'executed_at'])
