from django.db import models


class CSActivity(models.IntegerChoices):
    ACTIVITY_PAGEVIEW = 2, 'viewed a page'
    ACTIVITY_SEARCH = 3, 'queried'
    ACTIVITY_GENERATE_PAYMENT_LINK = 4, 'payment link'


class CSCorrectionRequestStatus(models.IntegerChoices):
    STATUS_NEW = 0, 'new'
    STATUS_ACCEPTED = 1, 'accepted'
    STATUS_REJECTED = 2, 'rejected'


class CSCorrectionRequestType(models.IntegerChoices):
    TYPE_AMOUNT_VAT = 0, 'Amount'
    TYPE_ITEMS = 1, 'Items'
    TYPE_ADDRESS = 2, 'Address'
    TYPE_NEUTRALIZATION = 3, 'Neutralization'
    TYPE_TO_B2B = 4, 'Normal VAT to B2B'
    TYPE_B2B_TO_NORMAL = 5, 'B2B to normal VAT'
    TYPE_ASSEMBLY = 6, 'Add assembly'
    TYPE_RECALCULATE = 7, 'Recalculation'
    TYPE_SWITCH = 8, 'Switch item'
    TYPE_FREE_RETURN = 9, 'Free return'
    TYPE_EXTRA_DISCOUNT = 10, 'Extra Discount'


class CSUnsuccessfulPaymentsStatus(models.IntegerChoices):
    NEW = 0, 'New'
    PAYED = 1, 'Payed'
    REJECTED = 2, 'Rejected'


class KlarnaPriceChangeType(models.IntegerChoices):
    INCREASE = 0, 'increase'
    DISCOUNT = 1, 'discount'


class KlarnaSource(models.IntegerChoices):
    ADD_EXTRA_DISCOUNT = 0, 'Add Extra Discount'
    CUSTOMER_SERVICE = 1, 'Customer Service'


class AbortRequestStatus(models.IntegerChoices):
    PENDING = 0, 'Pending'
    CANCELLED = 1, 'Cancelled'
    DONE = 2, 'Done'
