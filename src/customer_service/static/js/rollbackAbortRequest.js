document.addEventListener('DOMContentLoaded', () => {
  const rollbackAbortRequestForms = document.querySelectorAll(".rollback-abort-request-form");
  rollbackAbortRequestForms.forEach(postponeForm => {
    postponeForm.addEventListener("submit", (event) => {
      event.preventDefault();
      const form = event.currentTarget;

      fetch(form.action, {
        method: 'PATCH',
        body: "{}",
        headers: {
          'Content-Type': 'application/json'
        }
      })
        .then(response => {
          if (!response.ok) {
            return response.json().then(errorResponse => {
              throw new Error(errorResponse.error);
            });
          }
          return response.json();
        })
        .then(response => {
          window.location.reload();
        })
        .catch(error => {
          window.alert(error);
        });
    });
  });
});
