from typing import TYPE_CHECKING

from django.db import models

from customer_service.enums import AbortRequestStatus
from mailing.enums import CustomerTypeChoices
from mailing.managers import CustomerManager

if TYPE_CHECKING:
    from customer_service.models import OrderItemAbortRequest


class ReturningCustomerManager(CustomerManager):
    def get_queryset(self):
        return (
            super()
            .get_queryset()
            .filter(
                customer_type__in={
                    CustomerTypeChoices.CS_CUSTOMER,
                    CustomerTypeChoices.MAILING_AND_CS_CUSTOMER,
                },
            )
        )


class CSUserProfileManager(models.Manager):
    def update_or_create_from_user_profile(self, user_profile):
        return self.update_or_create(
            id=user_profile.id,
            defaults={
                'user_type': user_profile.user_type,
                'user_id': user_profile.user.id,
                'first_name': user_profile.first_name or '',
                'last_name': user_profile.last_name or '',
                'email': user_profile.email or '',
                'user_email': user_profile.user.email or '',
                'user_username': user_profile.user.username or '',
                'phone': user_profile.phone or '',
                'city': user_profile.city or '',
                'invoice_city': user_profile.invoice_city or '',
                'street_address_1': user_profile.street_address_1 or '',
                'invoice_street_address_1': user_profile.invoice_street_address_1 or '',
                'street_address_2': user_profile.street_address_2 or '',
                'invoice_street_address_2': user_profile.invoice_street_address_2 or '',
            },
        )


class CSOrderManager(models.Manager):
    def update_or_create_from_order(self, order):
        return self.update_or_create(
            id=order.id,
            defaults={
                'status': order.status,
                'first_name': order.first_name or '',
                'invoice_first_name': order.invoice_first_name or '',
                'last_name': order.last_name or '',
                'invoice_last_name': order.invoice_last_name or '',
                'company_name': order.company_name or '',
                'email': order.email or '',
                'invoice_email': order.invoice_email or '',
                'owner_email': order.owner.email or '',
                'phone': order.phone or '',
                'city': order.city or '',
                'street_address_1': order.street_address_1 or '',
                'invoice_street_address_1': order.invoice_street_address_1 or '',
                'street_address_2': order.street_address_2 or '',
                'invoice_street_address_2': order.invoice_street_address_2 or '',
                'owner_username': order.owner.username,
                'promo_text': order.promo_text or '',
                'product_ids': list(order.product_set.values_list('pk', flat=True)),
            },
        )


class OrderItemAbortRequestQuerySet(models.QuerySet):
    def pending(self) -> models.QuerySet['OrderItemAbortRequest']:
        return self.filter(status=AbortRequestStatus.PENDING, executed_at=None)
