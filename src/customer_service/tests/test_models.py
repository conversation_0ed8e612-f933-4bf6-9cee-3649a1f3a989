from decimal import Decimal

import pytest

from customer_service.correction_request_strategies import (
    CorrectionRequestAmountStrategy,
)
from customer_service.enums import AbortRequestStatus
from invoice.models import Invoice


@pytest.mark.django_db
@pytest.mark.nbp
def test_cs_correction_request_accept_correction_request_with_many_items(
    mocker,
    invoice_factory,
    invoice_item_factory,
    region_factory,
    cs_correction_request_factory,
    admin_user,
):
    correction_amount = Decimal('-100')
    mocker.patch('invoice.models.Invoice._validate_correction')
    invoice = invoice_factory(
        pretty_id='test/1/2/3',
        order__items=None,
        order__country='germany',
        order__region=region_factory(germany=True),
    )

    invoice_item_factory(
        invoice=invoice,
        gross_price=Decimal('975.61'),
    )

    invoice_item_factory(
        invoice=invoice,
        gross_price=Decimal('937.59'),
    )

    invoice_item_factory(
        invoice=invoice,
        gross_price=Decimal('881.95'),
    )

    cs_correction_request = cs_correction_request_factory(
        correction_amount_gross=correction_amount,
        invoice=invoice,
        issuer=admin_user,
    )
    mocker.patch(
        'customer_service.correction_request_strategies.AbstractCorrectionRequestStrategy._finalize_correction'
    )
    mocker.patch('invoice.models.Invoice._generate_pretty_id', return_value='test/1/2')
    strategy = CorrectionRequestAmountStrategy(cs_correction_request)
    strategy.prepare_correction_request()
    strategy.accept(user=admin_user)

    correction = Invoice.objects.filter(corrected_invoice=invoice).first()

    assert invoice.get_total_gross() - correction_amount == correction.get_total_gross()


@pytest.mark.django_db
class TestOrderItemAbortRequest:
    def test_update_status_to_cancelled_should_update_status_when_ok(
        self, order_item_abort_request_factory
    ):
        abort_request = order_item_abort_request_factory(
            status=AbortRequestStatus.PENDING
        )

        abort_request.update_status_to_cancelled()

        abort_request.refresh_from_db()
        assert abort_request.status == AbortRequestStatus.CANCELLED

    def test_update_status_to_done_should_update_status_when_ok(
        self, order_item_abort_request_factory
    ):
        abort_request = order_item_abort_request_factory(
            status=AbortRequestStatus.PENDING
        )

        abort_request.update_status_to_done()

        abort_request.refresh_from_db()
        assert abort_request.status == AbortRequestStatus.DONE
        assert abort_request.executed_at is not None
