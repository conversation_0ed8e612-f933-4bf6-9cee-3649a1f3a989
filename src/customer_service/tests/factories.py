from decimal import Decimal

import factory

from factory import fuzzy

from customer_service.enums import (
    AbortRequestStatus,
    CSCorrectionRequestStatus,
    CSCorrectionRequestType,
    KlarnaSource,
)
from invoice.enums import InvoiceItemTag
from orders.enums import OrderStatus
from orders.tests.factories import country_with_max_length


class CSCorrectionRequestFactory(factory.django.DjangoModelFactory):
    issuer = factory.SubFactory('user_profile.tests.factories.UserFactory')
    correction_amount_gross = Decimal(250)
    status = CSCorrectionRequestStatus.STATUS_NEW
    type_cs = CSCorrectionRequestType.TYPE_AMOUNT_VAT
    tag = InvoiceItemTag.DISCOUNT_QUALITY_DISSATISFACTION.value

    class Meta:
        model = 'customer_service.CSCorrectionRequest'


class CSCorrectionAddressRequestFactory(factory.django.DjangoModelFactory):
    first_name = factory.Faker('first_name')
    last_name = factory.Faker('last_name')
    street_address_1 = factory.Faker('address')
    street_address_2 = ''
    company_name = ''
    city = factory.Faker('city')
    postal_code = factory.Faker('postcode')
    country = factory.LazyFunction(country_with_max_length)
    vat = ''

    class Meta:
        model = 'customer_service.CSCorrectionAddressRequest'


class CSOrderFactory(factory.django.DjangoModelFactory):
    owner_username = factory.Faker('email')
    status = fuzzy.FuzzyChoice(
        choices=OrderStatus.values,
    )

    email = factory.Faker('email')
    owner_email = factory.Faker('email')

    first_name = factory.Faker('first_name')
    last_name = factory.Faker('last_name')
    street_address_1 = factory.Faker('address')
    street_address_2 = ''
    city = factory.Faker('city')
    phone = factory.Faker('phone_number')
    product_ids = factory.ListFactory()

    class Meta:
        model = 'customer_service.CSOrder'


class KlarnaAdjustmentFactory(factory.django.DjangoModelFactory):
    amount = Decimal('100.00')
    reason = 'test klarna adjustment'
    conversation_link = 'https://www.dixa.com/blablabla'
    source = KlarnaSource.CUSTOMER_SERVICE

    class Meta:
        model = 'customer_service.KlarnaAdjustment'


class OrderItemAbortRequestFactory(factory.django.DjangoModelFactory):
    order = factory.SubFactory('orders.tests.factories.OrderFactory')
    status = AbortRequestStatus.PENDING
    requested_by = factory.SubFactory('user_profile.tests.factories.UserFactory')

    class Meta:
        model = 'customer_service.OrderItemAbortRequest'
