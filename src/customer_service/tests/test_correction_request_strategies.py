from datetime import (
    UTC,
    datetime,
)
from decimal import Decimal

import pytest

from custom.models import Countries
from customer_service.correction_request_strategies import (
    AbstractCorrectionRequestStrategy,
    CorrectionRequestAddressStrategy,
    CorrectionRequestAmountStrategy,
)
from customer_service.enums import CSCorrectionRequestType
from invoice.choices import InvoiceStatus
from invoice.models import Invoice


class TestCorrectionRequestStrategies:
    @pytest.mark.django_db
    @pytest.mark.nbp
    def test_address_request_followed_by_amount_should_keep_changes_address(
        self,
        mocker,
        order_factory,
        invoice_factory,
        invoice_item_factory,
        region_factory,
        cs_correction_request_factory,
        cs_correction_address_request_factory,
        admin_user,
    ):
        mocker.patch('invoice.models.Invoice._validate_correction')
        order = order_factory(
            country='germany',
            region=region_factory(germany=True),
            items=None,
            first_name='First Name',
            last_name='Last Name',
            street_address_1='Street address 1',
            street_address_2='Street address 2',
            city='City',
            postal_code='Postal code',
        )

        invoice = invoice_factory(
            pretty_id='test/1/2/3',
            order=order,
        )

        invoice_item_factory(
            invoice=invoice,
            gross_price=Decimal('1000.0'),
        )
        mocker.patch(
            'customer_service.correction_request_strategies.AbstractCorrectionRequestStrategy._finalize_correction'
        )
        mocker.patch(
            'invoice.models.Invoice._generate_pretty_id', return_value='test/1/2'
        )

        address_request = cs_correction_address_request_factory(
            first_name='New First Name',
            last_name='New Last Name',
            street_address_1='New Street address 1',
            street_address_2='New Street address 2',
            city='New City',
            postal_code='New Postal code',
            country='New Germany',
        )
        cs_correction_request_address = cs_correction_request_factory(
            type_cs=CSCorrectionRequestType.TYPE_ADDRESS,
            invoice=invoice,
            issuer=admin_user,
            cs_correction_request=address_request,
        )

        strategy = CorrectionRequestAddressStrategy(cs_correction_request_address)
        strategy.prepare_correction_request()
        strategy.accept(user=admin_user)
        correction_invoice = Invoice.objects.get(corrected_invoice=invoice)
        correction_invoice.status = InvoiceStatus.CORRECTING
        correction_invoice.save(update_fields=['status'])
        assert (
            correction_invoice.additional_address_1.strip()
            == '''<h5>New First Name New Last Name </h5><p>New Street address 1 New Street address 2 </p>
            <p>New Postal code New City </p>
            <p>New Germany </p>'''.strip()
        )

        cs_correction_amount_request = cs_correction_request_factory(
            correction_amount_gross=Decimal('-100'),
            type_cs=CSCorrectionRequestType.TYPE_AMOUNT_VAT,
            invoice=correction_invoice,
            issuer=admin_user,
        )

        strategy = CorrectionRequestAmountStrategy(cs_correction_amount_request)
        strategy.prepare_correction_request()
        strategy.accept(user=admin_user)
        correction_invoice = Invoice.objects.get(
            previous_correction=correction_invoice,
            corrected_invoice=invoice,
        )
        assert (
            correction_invoice.additional_address_1
            == '''<h5>New First Name New Last Name </h5><p>New Street address 1 New Street address 2 </p>
            <p>New Postal code New City </p>
            <p>New Germany </p>'''.strip()
        )

    @pytest.mark.parametrize(
        'country,correction_amount_gross,sell_at,expected',
        [
            (
                Countries.poland.name,
                Decimal('-10.0'),
                datetime(2022, 12, 25, tzinfo=UTC),
                False,
            ),
            (
                Countries.finland.name,
                Decimal('-20.0'),
                datetime(2024, 8, 1, tzinfo=UTC),
                True,
            ),
            (
                Countries.finland.name,
                Decimal('-20.0'),
                datetime(2024, 10, 1, tzinfo=UTC),
                False,
            ),
            (
                Countries.finland.name,
                Decimal('20.0'),
                datetime(2024, 8, 1, tzinfo=UTC),
                False,
            ),
            (
                Countries.slovakia.name,
                Decimal('-20.0'),
                datetime(2024, 12, 1, tzinfo=UTC),
                True,
            ),
            (
                Countries.slovakia.name,
                Decimal('-20.0'),
                datetime(2025, 2, 1, tzinfo=UTC),
                False,
            ),
            (
                Countries.slovakia.name,
                Decimal('20.0'),
                datetime(2024, 12, 1, tzinfo=UTC),
                False,
            ),
            (
                Countries.estonia.name,
                Decimal('-20.0'),
                datetime(2025, 6, 1, tzinfo=UTC),
                True,
            ),
            (
                Countries.estonia.name,
                Decimal('-20.0'),
                datetime(2025, 8, 1, tzinfo=UTC),
                False,
            ),
            (
                Countries.estonia.name,
                Decimal('20.0'),
                datetime(2025, 6, 1, tzinfo=UTC),
                False,
            ),
        ],
    )
    def test_is_country_before_vat_change_for_additional_payment_should_return_expected_when_called(
        self,
        country,
        correction_amount_gross,
        sell_at,
        expected,
        cs_correction_request_factory,
        invoice_factory,
        order_factory,
    ):

        order = order_factory(country=country)
        correction_invoice = invoice_factory(pretty_id='test/1/2/3', order=order)
        invoice = invoice_factory(
            pretty_id='test/2/2/3',
            sell_at=sell_at,
            order=order,
        )
        cs_correction_request_address = cs_correction_request_factory(
            type_cs=CSCorrectionRequestType.TYPE_AMOUNT_VAT,
            correction_invoice=correction_invoice,
            invoice=invoice,
            correction_amount_gross=correction_amount_gross,
        )

        is_before_vat = AbstractCorrectionRequestStrategy.is_country_before_vat_change_for_additional_payment(
            cs_correction_request_address
        )

        assert is_before_vat is expected
