import logging

from collections import OrderedDict
from datetime import (
    date,
    datetime,
    timedelta,
)
from decimal import (
    ROUND_HALF_UP,
    Decimal,
)
from typing import (
    Iterable,
    Optional,
)

from django.conf import settings
from django.contrib import messages
from django.contrib.auth.decorators import user_passes_test
from django.contrib.auth.models import User
from django.contrib.messages.views import SuccessMessageMixin
from django.core.cache import cache
from django.db import (
    IntegrityError,
    transaction,
)
from django.db.models import Prefetch
from django.http import (
    HttpResponse,
    HttpResponseRedirect,
    JsonResponse,
)
from django.shortcuts import (
    get_object_or_404,
    redirect,
)
from django.urls import (
    reverse,
    reverse_lazy,
)
from django.utils import timezone
from django.utils.decorators import method_decorator
from django.utils.html import mark_safe
from django.views.generic import (
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    FormView,
    Template<PERSON>iew,
    UpdateView,
    View,
)
from django.views.generic.base import (
    ContextMixin,
    TemplateResponseMixin,
)
from django.views.generic.detail import DetailView
from django.views.generic.list import ListView

from past.utils import old_div

from carts.models import Cart
from carts.services.cart_service import CartService
from complaints.enums import (
    DamageFormStatus,
    ImageType,
)
from complaints.models import (
    CustomerContact,
    DamageData,
    DamageImage,
)
from complaints.tasks import notify_logistic_about_damage_form_ready
from custom.enums import Furniture
from custom.utils.exchange import (
    EuroRateNotFound,
    convert_pln_to_euro,
)
from custom.utils.exports import dump_list_as_txt
from customer_service import filter_sets
from customer_service.enums import (
    CSActivity,
    CSUnsuccessfulPaymentsStatus,
)
from customer_service.forms import (
    AddSampleBoxForm,
    ChangeLanguageForm,
    ChangeOrderStatusForm,
    CSCreateDamageForm,
    CSFreeReturnCreateForm,
    CSNote,
    CSOrderUpdateForm,
    DeactivationUserForm,
    InvoiceItemsFormSet,
    KlarnaAdjustmentCreateForm,
    NewsletterUnsubscribeForm,
    OrderAddExtraDiscountVoucherReplacementForm,
    OrderForm,
    OrderInvoiceAddressForm,
    OrderSwitchItemOnHoldForm,
    OrderSwitchItemReplacementForm,
    OrderSwitchRecalculationsForm,
    ProformaForm,
    PromocodeAddForm,
    RegionCalculatorForm,
    UserProfileIsBusinessTypeForm,
    UserSearchForm,
)
from customer_service.internal_api.clients import (
    GetTransportCostBaseOnValueAndWeightAPIClient,
)
from customer_service.internal_api.events import LogisticOrderMailingDisabledEvent
from customer_service.mixins import OrderSwitchLogCustomerServiceViewMixin
from customer_service.models import (
    CSActivityLog,
    CSCorrectionRequest,
    CSOrder,
    CSUserProfile,
)
from customer_service.reviews import (
    TrustedShopReviews,
    TrustPilotReviews,
)
from customer_service.tasks import send_email_missing_invoice
from customer_service.utils import (
    get_filters_from,
    query_label_for,
    update_order_and_product_notes_from_cs_notes,
)
from events.choices import BrazeSubscriptionStates
from events.domain_events.marketing_events import (
    EmailSubscriptionEvent,
    LanguageUpdateEvent,
)
from free_returns.enums import FreeReturnStatusChoices
from gallery.enums import FurnitureStatusEnum
from gallery.models import (
    Jetty,
    SampleBox,
)
from invoice.choices import (
    InvoiceSource,
    InvoiceStatus,
)
from invoice.models import Invoice
from logger.views import LoggerViewMixin
from orders.enums import OrderStatus
from orders.forms import SendOrderNotificationsForm
from orders.mixins import ExtraDiscountStatus
from orders.models import (
    Order,
    OrderItem,
    PaidOrders,
    VIPOrder,
)
from orders.services.order_abort import request_order_abort
from orders.switch_status import (
    SwitchStatus,
    is_order_promo_inactive,
)
from payments.models import Notification
from pricing_v3.services.item_price_calculators import OrderItemPriceCalculator
from pricing_v3.services.price_calculators import OrderPriceCalculator
from producers.choices import (
    ProductPriority,
    ProductStatus,
    SourcePriority,
)
from producers.models import Product
from producers.tasks import generate_instruction_task
from user_profile.choices import UserType
from user_profile.models import UserProfile
from vouchers.enums import (
    ServiceType,
    VoucherOrigin,
)
from vouchers.exceptions import VoucherCodeUniquenessError
from vouchers.models import (
    ItemDiscount,
    Voucher,
    VoucherBarterDeal,
)

logger = logging.getLogger('cstm')

CS_CACHE_NOTE = 'cs_note'


def get_cs_correction_request_queryset():
    return CSCorrectionRequest.objects.select_related(
        'issuer',
        'reviewer',
        'invoice',
        'invoice__order',
    )


def customer_service_required(function=None):
    def login_check(user):
        return user.is_authenticated and (
            user.profile.user_type == UserType.CUSTOMER_SERVICE or user.is_superuser
        )

    _decorator = user_passes_test(
        login_check,
        login_url=reverse_lazy('cs_login'),
        redirect_field_name='next',
    )
    return _decorator(function) if function else _decorator


class CustomerServiceMixinView(
    LoggerViewMixin,
    TemplateResponseMixin,
    ContextMixin,
    View,
):
    filter_set_classes = {
        'orders': filter_sets.CSOrderSearchFilterSet,
        'userprofiles': filter_sets.CSUserProfileSearchFilterSet,
        'invoices': filter_sets.CSInvoiceSearchFilterSet,
        'carts': filter_sets.CSCartSearchFilterSet,
    }
    filter_names_prioritized = (
        'q_firstname',
        'q_lastname',
        'q_company_name',
        'q_email',
        'q_phone',
        'q_street',
        'q_city',
        'q_productid',
        'q_promo_text',
        'q_invoice_pretty_id',
    )

    USER_TYPE_LOOKUPS = (
        ('all', 'All'),
        ('customer', 'Customer'),
        ('guest', 'Guest'),
    )

    list_filter = (USER_TYPE_LOOKUPS,)

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['filters'] = get_filters_from(
            [cls() for cls in list(self.filter_set_classes.values())],
            self.filter_names_prioritized,
        )
        context['list_filter'] = self.list_filter
        return context

    @method_decorator(customer_service_required)
    def dispatch(self, request, *args, **kwargs):
        CSActivityLog.objects.create(
            activity_context=request.path[:1024],
            activity_type=CSActivity.ACTIVITY_PAGEVIEW,
            user=request.user,
        )
        return super().dispatch(request, *args, **kwargs)

    def init_filter_sets(self, request):
        _filter_sets = {}
        for key, filter_set_class in list(self.filter_set_classes.items()):
            queryset = getattr(self, f'get_{key}_queryset')(request.GET)
            _filter_sets[key] = filter_set_class(
                request=request,
                data=request.GET,
                queryset=queryset,
            )
        return _filter_sets

    def get_orders_queryset(self, request_get):
        return CSOrder.objects.all()

    def get_carts_queryset(self, request_get):
        return Cart.objects.all()

    def get_userprofiles_queryset(self, request_get):
        user_type = request_get.get('user_type', '')
        if user_type == 'customer':
            return CSUserProfile.objects.filter(user_type=UserType.CUSTOMER)
        elif user_type == 'guest':
            return CSUserProfile.objects.filter(user_type=UserType.GUEST_CUSTOMER)
        return CSUserProfile.objects.all()

    def get_invoices_queryset(self, request_get):
        return Invoice.objects.select_related('order')


class CustomerServiceDashboardView(CustomerServiceMixinView, TemplateView):
    template_name = 'customer_service/dashboard.html'
    LIMIT_TO = 10

    def get(self, request, *args, **kwargs):
        context = self.get_dashboard_context_data(kwargs, request)
        cs_note = cache.get(CS_CACHE_NOTE, '')
        context['cs_note_form'] = CSNote(initial={'cs_note': cs_note})
        context['cs_note'] = cs_note.replace('\n', '</br>')
        return self.render_to_response(context=context)

    def post(self, request, *args, **kwargs):
        context = self.get_dashboard_context_data(kwargs, request)
        cs_note = cache.get(CS_CACHE_NOTE, '')
        context['cs_note'] = cs_note.replace('\n', '</br>')
        form = CSNote(request.POST)
        if form.is_valid() and form.cleaned_data['cs_note']:
            cache.set(CS_CACHE_NOTE, form.cleaned_data['cs_note'], timeout=None)
            context['cs_note'] = form.cleaned_data['cs_note'].replace('\n', '</br>')

        context['cs_note_form'] = form
        return self.render_to_response(context=context)

    def get_dashboard_context_data(self, kwargs, request):
        context = self.get_context_data(**kwargs)
        # TODO: move it to `.get_context_data()`
        context['correction_requests'] = get_cs_correction_request_queryset().order_by(
            '-id'
        )[: self.LIMIT_TO]
        context['payment_notifications_error'] = Notification.objects.filter(
            code='AUTHORISATION',
            success=False,
        ).order_by('-event_date')[: self.LIMIT_TO]
        context['orders_delivered'] = self.get_order_queryset().filter(
            status=OrderStatus.DELIVERED,
        )[: self.LIMIT_TO]
        context['orders_shipped'] = self.get_order_queryset().filter(
            status=OrderStatus.SHIPPED,
        )[: self.LIMIT_TO]
        context['orders_to_be_shipped'] = self.get_order_queryset().filter(
            status=OrderStatus.TO_BE_SHIPPED,
        )[: self.LIMIT_TO]
        context['vip_orders'] = self.get_vip_order_queryset()[: self.LIMIT_TO]
        return context

    def get_order_queryset(self):
        queryset = Order.objects.select_related(
            'owner',
            'owner__profile',
            'region',
        )
        return queryset.prefetch_related(
            'invoice_set',
            'items',
            'region__countries',
            'suborders',
        )

    def get_vip_order_queryset(self):
        queryset = VIPOrder.objects.select_related(
            'owner',
            'owner__profile',
            'region',
        )
        return queryset.prefetch_related(
            'invoice_set',
            'items',
            'region__countries',
            'suborders',
        )


class CustomerServiceSearchView(CustomerServiceMixinView, TemplateView):
    template_name = 'customer_service/search.html'
    LIMIT_TO = 100

    def get(self, request, *args, **kwargs):
        context = self.get_context_data(**kwargs)

        _filter_sets = self.init_filter_sets(request)
        results = OrderedDict(
            [
                (key, _filter_sets[key].qs[: self.LIMIT_TO])
                for key in ['orders', 'userprofiles', 'invoices', 'carts']
            ]
        )

        CSActivityLog.objects.create(
            activity_context=request.path[:1024],
            activity_type=CSActivity.ACTIVITY_SEARCH,
            user=request.user,
        )
        context['query'] = query_label_for(list(_filter_sets.values()))
        context['results'] = results
        return self.render_to_response(context=context)


class GetCostWithTransport(View):
    http_method_names = ('get',)

    @staticmethod
    def get(request, product_id, *args, **kwargs):
        product = get_object_or_404(Product, pk=product_id)
        country = product.order_item.region.countries.last()
        cost = Decimal(request.GET.get('cost', '0'))
        weight = Decimal(request.GET.get('weight', '0'))
        packages = product.get_packaging()
        country_name = country.name if country else ''

        api_client = GetTransportCostBaseOnValueAndWeightAPIClient()
        transport_cost = api_client.get_cost(country_name, cost, weight, packages)

        try:
            response_data = {
                'total_cost': str(convert_pln_to_euro(transport_cost + cost)),
                'transport_cost': str(convert_pln_to_euro(transport_cost)),
            }
        except EuroRateNotFound:
            return HttpResponse(
                reason='Unable to convert pln to euro, try later', status=400
            )
        return JsonResponse(response_data)


class UserOverviewView(CustomerServiceMixinView, TemplateView):
    template_name = 'customer_service/user_overview.html'
    order_statuses = {
        OrderStatus.IN_PRODUCTION,
        OrderStatus.SHIPPED,
        OrderStatus.DELIVERED,
        OrderStatus.TO_BE_SHIPPED,
    }

    def get(self, request, pk=None):
        from user_profile.models import LoginAccessToken

        if pk is None or not pk.isdigit():
            messages.error(request, 'Could not find user_id {}'.format(pk))
            return redirect(reverse('cs_dashboard'))

        context = self.get_context_data()

        payment = False
        user = self.get_user(pk)
        lat = None
        payment_get = request.GET.get('payment_link') == 'True'
        is_cart = request.GET.get('is_cart', False)
        order_id = request.GET.get('order_id')
        order = Order.objects.filter(id=order_id, owner=user).first()
        cart = self.get_user_cart(user)

        if payment_get:
            if is_cart and cart:
                order = cart.order or CartService(cart).sync_with_order()

            if order.status in self.order_statuses:
                return redirect(reverse('cs_dashboard'))
            payment = True

            lat = LoginAccessToken.get_or_create_for_user(user)

            order.change_status(OrderStatus.DRAFT)
            order.save()
            msg = 'Genereted links for order {} and user_id {}'.format(
                order.pk,
                user.id,
            )
            self.log_cs_data(
                request,
                'cs_payment_link',
                order._meta.label,
                order.pk,
                {'user_id': pk},
                additional_info=None,
            )
            CSActivityLog.objects.create(
                activity_type=CSActivity.ACTIVITY_GENERATE_PAYMENT_LINK,
                user=request.user,
                activity_context=msg,
            )
        context['user'] = user
        context['cart'] = cart
        context['orders'] = user.order_set.exclude(status=OrderStatus.CART)
        context['payment_link'] = payment
        context['lat'] = lat
        context['order'] = order

        return self.render_to_response(context=context)

    def get_user(self, pk):
        queryset = User.objects.select_related(
            'profile',
        )
        queryset = queryset.prefetch_related(
            Prefetch('jetty_set', self.get_jetty_queryset()),
            Prefetch('order_set', self.get_order_queryset()),
            Prefetch('order_set__invoice_set', self.get_invoice_queryset()),
            Prefetch(
                'order_set__items__product_set',
                self.get_product_queryset(),
            ),
            Prefetch('order_set__product_set', self.get_product_queryset()),
            'order_set__invoice_set__corrections',
            'order_set__invoice_set__cscorrectionrequest_set',
            'order_set__invoice_set__invoice_items',
            'order_set__invoice_set__invoice_items__correcting_items',
            'order_set__invoice_set__moneycashback_set',
            'order_set__items',
            'order_set__moneytransfer_set',
            'order_set__product_set__complaint_set',
            'order_set__region__countries',
            'order_set__transactions',
            'order_set__transactions__notification_set',
            'profile__userprofile_set',
        )
        return queryset.get(pk=pk)

    def get_order_queryset(self):
        return Order.objects.select_related('region')

    def get_invoice_queryset(self):
        return Invoice.objects.select_related(
            'corrected_invoice',
            'previous_correction',
        )

    def get_product_queryset(self):
        return Product.objects.select_related(
            'product_details_jetty',
            'manufactor',
        )

    def get_jetty_queryset(self):
        return Jetty.objects.select_related('owner', 'owner__profile')

    def get_user_cart(self, user):
        return CartService.get_cart(user, prefetch_related_fields=['items'])


class ChangeOrderOwnerView(CustomerServiceMixinView, TemplateView):
    template_name = 'customer_service/edit_order_owner.html'

    def get(self, request, pk):
        context = self.get_context_data()
        context['order'] = Order.objects.get(pk=pk)
        context['form'] = UserSearchForm()
        return self.render_to_response(context=context)

    def post(self, request, pk):
        order = Order.objects.get(pk=pk)
        form = UserSearchForm(request.POST)
        prev_user = int(order.owner.pk)

        context = self.get_context_data()
        context['order'] = order
        context['form'] = form

        if form.is_valid():
            user = User.objects.filter(pk=form.cleaned_data['user_id'])
            if user.count() == 1:
                user = user[0]
                note = 'Changed by -{}- ({}) - owner from {} to {}<br/>'
                note = note.format(
                    request.user,
                    datetime.today().isoformat(),
                    order.owner.username,
                    user.username,
                )
                order.owner = user
                if order.order_notes:
                    order.order_notes += note
                else:
                    order.order_notes = note
                order.save()

                order_items = order.items.all()
                jettys = [oi.order_item for oi in order_items]
                for jetty in jettys:
                    jetty.owner = order.owner
                    jetty.save()
                self.log_cs_data(
                    request,
                    'cs_order_owner',
                    order._meta.label,
                    order.pk,
                    {'user_id': user.pk, 'prev_user': prev_user},
                    additional_info=[j.pk for j in jettys],
                )
                messages.info(request, 'Success')
                return redirect(
                    reverse('cs_user_overview', args=[order.owner_id]),
                )
            else:
                messages.info(
                    request,
                    'Cant find user {}'.format(form.cleaned_data['user_id']),
                )

                return self.render_to_response(context=context)
        messages.info(request, 'Please correct errors below')
        return self.render_to_response(context=context)


class CreateFreeReturnView(
    SuccessMessageMixin,
    CustomerServiceMixinView,
    CreateView,
):
    template_name = 'customer_service/create_free_return.html'
    form_class = CSFreeReturnCreateForm
    success_message = 'Free Return created'

    def get_form(self, form_class=None):
        if form_class is None:
            form_class = self.get_form_class()
        form_kwargs = self.kwargs.copy()
        form_kwargs.update(self.get_form_kwargs())
        return form_class(**form_kwargs)

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        try:
            context['order'] = Order.objects.get(pk=self.kwargs['pk'])
        except Order.DoesNotExist:
            return HttpResponse('no order with such pk')
        return context

    def form_valid(self, form):
        if form.is_valid():
            self.object = form.save(commit=False)
            self.object.note = 'created'
            self.object.status = FreeReturnStatusChoices.NEW
            order_items = form.cleaned_data['order_item']
            order = order_items[0].order
            self.object.save()
            if not Invoice.objects.filter(
                status__in=[
                    InvoiceStatus.ENABLED,
                    InvoiceStatus.CORRECTING,
                    InvoiceStatus.ENABLED_VAT_REGION_CORRECTION,
                ],
                order=order,
            ).exists():
                # Case for Klarna when invoice hasn't been generated yet
                send_email_missing_invoice.delay(order.id, self.object.id)
            for order_item in order_items:
                order_item.free_return = self.object
                order_item.save()

            data = self.prepare_form_data(form.cleaned_data)
            self.log_cs_data(
                self.request,
                'cs_create_free_return',
                self.object._meta.label,
                self.object.pk,
                data,
                {},
            )
        return super().form_valid(form)

    def get_success_url(self):
        return reverse(
            'cs_user_overview',
            args=(Order.objects.get(pk=self.kwargs['pk']).owner_id,),
        )


class SendOrderNotificationsFormView(FormView, DetailView):
    form_class = SendOrderNotificationsForm
    template_name = 'customer_service/send_order_notifications.html'
    model = Order

    def __init__(self):
        super().__init__()
        self._object = None

    @property
    def order(self):
        if not self._object:
            self._object = self.get_object()
        return self._object

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['owner_id'] = self.order.owner_id
        return context

    def get_initial(self):
        initial = super().get_initial()
        initial['email_address'] = self.order.email
        return initial

    def form_valid(self, form):
        error = self.order.send_order_notifications(
            email_address=form.cleaned_data['email_address'],
            order_confirmation=form.cleaned_data['order_confirmation'],
            payment_confirmation=form.cleaned_data['payment_confirmation'],
            invoice_email=form.cleaned_data['invoice_email'],
        )
        if error:
            messages.error(self.request, error)
        else:
            messages.info(
                self.request,
                f'Send email notifications for order: {self.order.id}',
            )
        return super().form_valid(form)

    def get_success_url(self):
        return reverse('cs_user_overview', args=(self.order.owner_id,))


class CreateDamageFormView(
    SuccessMessageMixin,
    CustomerServiceMixinView,
    CreateView,
):
    template_name = 'customer_service/create_damage_form.html'
    form_class = CSCreateDamageForm
    model = DamageData

    success_message = 'Damage form created'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        obj = get_object_or_404(Product, pk=self.kwargs['pk'])
        context['order'] = obj.order_id
        return context

    def form_valid(self, form):
        object = form.save(commit=False)
        object.product = Product.objects.get(pk=self.kwargs['pk'])

        if self.request.POST.get('submit') == 'Create and Send':
            object.status = DamageFormStatus.READY

            object.ready_since = timezone.now()
            object.save()
            notify_logistic_about_damage_form_ready.delay(
                object.id, object.product.order.id
            )

        object.save()

        for image in self.request.FILES.getlist('damaged_packaging'):
            DamageImage.objects.create(
                image=image,
                damage_form_data=object,
                image_type=ImageType.DAMAGED_PACKAGE.value,
            )

        for image in self.request.FILES.getlist('damaged_elements'):
            DamageImage.objects.create(
                image=image,
                damage_form_data=object,
                image_type=ImageType.DAMAGED_ELEMENTS.value,
            )

        return super().form_valid(form)

    def get_success_url(self):
        return reverse('cs_user_overview', args=[self.object.product.order.owner_id])


class UpdateOrderView(SuccessMessageMixin, CustomerServiceMixinView, UpdateView):
    template_name = 'customer_service/update_order.html'
    form_class = CSOrderUpdateForm
    success_url = '/cs/'
    model = Order
    success_message = 'Order updated'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['order'] = Order.objects.get(pk=self.kwargs['pk'])
        return context

    def form_valid(self, form):
        if form.is_valid():
            self.object = form.save(commit=False)
            self.object.save()
            data = self.prepare_form_data(form.cleaned_data)
            self.log_cs_data(
                self.request,
                'cs_update_order',
                self.object._meta.label,
                self.object.pk,
                data,
                additional_info=None,
            )
        return super().form_valid(form)

    def get_success_url(self):
        return reverse('cs_user_overview', args=[self.object.owner_id])


class ChangeOrderToPending(SuccessMessageMixin, CustomerServiceMixinView, View):
    def get(self, request, pk=None):
        if pk is None:
            return redirect(reverse('cs_dashboard'))

        cart = Cart.objects.get(pk=pk)
        order = CartService(cart).sync_with_order()
        order.change_status(OrderStatus.PAYMENT_PENDING)
        messages.info(request, 'Order %s changed to pending' % order.id)
        self.log_cs_data(
            request,
            'cs_change_order_to_pending',
            order._meta.label,
            order.pk,
            {'before_status': 'cart'},
            additional_info=None,
        )
        return redirect(request.META.get('HTTP_REFERER', reverse('cs_dashboard')))


class CustomerServicePendingOrders(CustomerServiceMixinView, ListView):
    template_name = 'customer_service/pending_orders.html'
    LIMIT_TO = 50

    def get_queryset(self):
        queryset = Order.objects.select_related(
            'owner',
            'owner__profile',
            'region',
        )
        queryset = queryset.prefetch_related(
            'items',
            'region__countries',
        )
        queryset = queryset.filter(
            status=OrderStatus.PAYMENT_PENDING,
            created_at__gte=timezone.now() - timedelta(weeks=12),
        )
        return queryset[: self.LIMIT_TO]


class CustomerServicePromoPaidOrders(CustomerServiceMixinView, ListView):
    template_name = 'customer_service/promo_paid_orders.html'
    LIMIT_TO = 30

    def get_queryset(self):
        queryset = PaidOrders.objects.select_related(
            'owner',
            'owner__profile',
            'region',
        )
        queryset = queryset.prefetch_related(
            'items',
            'region__countries',
        )
        queryset = queryset.filter(used_promo__origin=VoucherOrigin.CUSTOMER_SUPPORT)
        return queryset.order_by('-paid_at')[: self.LIMIT_TO]


class CustomerServiceCorrectionList(CustomerServiceMixinView, ListView):
    template_name = 'customer_service/requested_corrections.html'
    filter_set_class = filter_sets.CSCorrectionRequestFilterSet

    def get_context_data(self, **kwargs):
        kwargs['from_datetime'] = None
        if 'from_last_days' in self.request.GET:
            kwargs['from_datetime'] = timezone.now() - timedelta(
                days=int(self.request.GET['from_last_days'])
            )
        return super().get_context_data(**kwargs)

    def get_queryset(self):
        queryset = get_cs_correction_request_queryset()
        filter_set = self.filter_set_class(
            data=self.request.GET,
            queryset=queryset,
            request=self.request,
        )
        return filter_set.qs.order_by('-created_at')


class CreateProformaAndSendView(SuccessMessageMixin, CustomerServiceMixinView, View):
    def get(self, request, pk=None, dont_sent=False):
        if pk is None:
            return redirect(reverse('cs_dashboard'))

        order = Order.objects.get(pk=pk)
        if order.invoice_set.all().filter(status=InvoiceStatus.PROFORMA).count() > 0:
            messages.error(request, 'Proforma already created for this order')
            return redirect(request.META.get('HTTP_REFERER', reverse('cs_dashboard')))

        before_status = order.status
        if order.status in [OrderStatus.CART, OrderStatus.CANCELLED]:
            order.change_status(OrderStatus.PAYMENT_PENDING)

        invoice = order.create_proforma_invoice(source=InvoiceSource.CUSTOMER_SERVICE)

        self.log_cs_data(
            request,
            'cs_create_proforma',
            invoice._meta.label,
            invoice.pk,
            {'pk': pk, 'dont_sent': dont_sent},
            {'order_status': order.status, 'before_status': before_status},
        )

        if dont_sent:
            messages.info(request, 'Proforma created')
        else:
            invoice_for_client = (
                invoice.get_domestic_invoice()
                if order.earliest_invoice_domestic_version_supported()
                else invoice
            )
            invoice_for_client.send_invoice(order)
            messages.info(request, f'Proforma sent to {order.email}')

        return redirect(request.META.get('HTTP_REFERER', reverse('cs_dashboard')))


class DesignChangedView(CustomerServiceMixinView, View):
    @classmethod
    def post(cls, request, pk=None):
        order = get_object_or_404(Order, pk=pk)
        products = order.product_set.filter(priority=ProductPriority.ON_HOLD_DES)
        url = request.META.get('HTTP_REFERER', reverse('cs_dashboard'))
        if not products.exists():
            messages.error(request, 'No products waiting for design change')
            return redirect(url)
        product_ids = ', '.join(
            [str(_id) for _id in products.values_list('id', flat=True)]
        )
        for product in products:
            product.priority_updater.change_priority(
                ProductPriority.NORMAL, owner=request.user
            )
        messages.info(
            request, f'Priority for products: {product_ids} changed to NORMAL'
        )
        return redirect(url)


class AssemblyInstructionView(CustomerServiceMixinView, View):
    @classmethod
    def post(cls, request, pk=None):
        product = get_object_or_404(Product, pk=pk)
        generate_instruction_task(product.id)
        messages.info(
            request, 'If instruction is not ready, please way few minutes and refresh'
        )
        return redirect(request.META.get('HTTP_REFERER', reverse('cs_dashboard')))


class ChangeProductPriorityView(SuccessMessageMixin, CustomerServiceMixinView, View):
    def post(self, request, pk, priority):
        product = Product.objects.get(pk=pk)
        available_priorities = [prio.value for prio in ProductPriority]
        if (
            product.status == ProductStatus.NEW.value
            and int(priority) in available_priorities
            and int(priority) != product.priority
        ):
            product.priority_updater.change_priority(
                int(priority),
                owner=request.user,
            )
            messages.info(request, 'Priority changed')
        else:
            messages.info(request, 'Failed during changing priority')
        return redirect(
            reverse('cs_user_overview', args=[product.order.owner_id]),
        )


class ChangeSourcePriorityView(SuccessMessageMixin, CustomerServiceMixinView, View):
    def post(self, request, pk, priority):
        product = Product.objects.get(pk=pk)
        available_priorities = [prio.value for prio in SourcePriority]
        if (
            product.status == ProductStatus.NEW.value
            and int(priority) in available_priorities
            and int(priority) != product.source_priority
        ):
            product.priority_updater.change_source_priority(
                SourcePriority(int(priority)),
                user=request.user,
            )
            messages.info(request, 'Priority changed')
        else:
            messages.info(request, 'Failed during changing priority')
        return redirect(
            reverse('cs_user_overview', args=[product.order.owner_id]),
        )


class ChangeUserLangView(SuccessMessageMixin, CustomerServiceMixinView, FormView):
    template_name = 'customer_service/change_user_lang.html'
    form_class = ChangeLanguageForm

    def get(self, request, user_id, *args, **kwargs):
        self.initial = {'user_id': user_id}
        return super().get(request, user_id, *args, **kwargs)

    def form_valid(self, form):
        up = UserProfile.objects.get(user_id=form.cleaned_data['user_id'])
        up.language = form.cleaned_data['language']
        up.save(update_fields=['language'])
        self.success_url = reverse(
            'cs_user_overview', args=[form.cleaned_data['user_id']]
        )
        self.success_message = self.success_message.format(
            form.cleaned_data['language']
        )

        LanguageUpdateEvent(user=up.user, language=up.language)
        return super().form_valid(form)


class ChangeOrderStatusView(SuccessMessageMixin, CustomerServiceMixinView, FormView):
    template_name = 'customer_service/change_order_status.html'
    form_class = ChangeOrderStatusForm

    def get(self, request, order_id, *args, **kwargs):
        order = get_object_or_404(Order, pk=order_id)
        self.initial = {'status': order.status}
        return super().get(request, order_id, *args, **kwargs)

    def form_valid(self, form):
        order = get_object_or_404(Order, pk=self.kwargs.get('order_id'))
        order.change_status(int(form.cleaned_data['status']))

        self.log_cs_data(
            self.request,
            'change_status',
            order._meta.label,
            order.id,
            {'old_status': order.status_previous, 'new_status': order.status},
            additional_info=None,
        )
        self.success_url = reverse('cs_user_overview', args=[order.owner_id])
        self.success_message = self.success_message.format(form.cleaned_data['status'])
        return super().form_valid(form)


class StopMailingFlowView(SuccessMessageMixin, CustomerServiceMixinView, View):
    def get(self, request, pk=None):
        if pk is None:
            return redirect(reverse('cs_dashboard'))
        order = Order.objects.get(pk=pk)
        logistic_order = order.last_logistic_order
        if not logistic_order:
            messages.warning(request, 'Logistic order not found')
            return redirect(reverse('cs_user_overview', args=[order.owner_id]))
        if logistic_order.mailing_disabled:
            messages.warning(request, 'Mailing flow already stopped')
        else:
            LogisticOrderMailingDisabledEvent(order, logistic_order.id)
            messages.info(request, 'Mailing flow disabled')
            self.log_cs_data(
                request,
                'cs_stop_mailing_flow',
                'logistic.LogisticOrder',
                logistic_order.id,
                {'order_id': pk},
                additional_info=None,
            )
        return redirect(reverse('cs_user_overview', args=[order.owner_id]))


class SendInvoiceView(SuccessMessageMixin, CustomerServiceMixinView, View):
    def get(self, request, pk=None):
        if pk is None:
            return redirect(reverse('cs_dashboard'))

        invoice = Invoice.all_objects.get(pk=pk)
        invoice.send_invoice_to_user()
        messages.info(
            request,
            'Invoice {} sent to {}'.format(
                invoice.pretty_id,
                invoice.order.email,
            ),
        )
        self.log_cs_data(
            request,
            'cs_send_invoce',
            invoice._meta.label,
            invoice.pk,
            {'email': invoice.order.email},
            additional_info=None,
        )
        return redirect(
            request.META.get('HTTP_REFERER', reverse('cs_dashboard')),
        )


class PromocodeAddView(CustomerServiceMixinView, TemplateView):
    form_class = PromocodeAddForm
    template_name = 'customer_service/add_promocode.html'

    def get(self, request, *args, **kwargs):
        context = self.get_context_data(**kwargs)
        context['active_vouchers'] = Voucher.objects.filter(quantity_left__gt=0).count()
        context['invite_vouchers'] = Voucher.objects.filter(origin=1).count()
        context['form'] = self.form_class()
        return self.render_to_response(context=context)

    def post(self, request, *args, **kwargs):
        form = self.form_class(request.POST, use_required_attribute=True)
        context = self.get_context_data(**kwargs)
        context['active_vouchers'] = Voucher.objects.filter(quantity_left__gt=0).count()
        context['invite_vouchers'] = Voucher.objects.filter(origin=1).count()
        context['form'] = form

        if form.is_valid():
            how_many = form.cleaned_data['how_many']
            vouchers = []
            codes = []
            if how_many == 1:
                code = form.cleaned_data.get('code', None) or Voucher.generate_code()
                voucher = self._create_single_voucher(form, code)
                if voucher:
                    codes.append(voucher.code)
                    vouchers.append(voucher)
            else:
                codes = self._generate_codes(form, how_many)
                vouchers = self._create_vouchers(form, codes)
                if vouchers:
                    messages.success(
                        request,
                        f'Successfully added {how_many} promocodes',
                    )

            email = form.cleaned_data['email']
            if email and vouchers:
                dump_list_as_txt(
                    codes,
                    output='vouchers_codes.txt',
                    mail=(email,),
                    mail_body='Requested codes attached.',
                    mail_subject='Requested codes',
                )
            context['vouchers'] = vouchers
        return self.render_to_response(context)

    def _create_delivery_discount(
        self, form: PromocodeAddForm, vouchers: list[Voucher]
    ) -> None:
        item_discount, _ = ItemDiscount.objects.get_or_create(
            value=form.cleaned_data['delivery_discount'],
            service_type=ServiceType.DELIVERY,
        )
        item_discount.voucher_set.add(*vouchers)

    def _create_barter_deals(
        self,
        form: PromocodeAddForm,
        vouchers: Iterable[Voucher],
    ) -> None:
        barter_deals = []
        for voucher in vouchers:
            barter_deal = VoucherBarterDeal(
                voucher=voucher,
                value=form.cleaned_data['barter_value'],
                currency_id=form.cleaned_data['barter_currency'],
            )
            barter_deals.append(barter_deal)
        VoucherBarterDeal.objects.bulk_create(barter_deals, batch_size=100)

    def _create_single_voucher(self, form: PromocodeAddForm, code):
        create_params = self._get_voucher_create_params(form)
        try:
            voucher = Voucher.objects.create(code=code, **create_params)
            messages.success(
                self.request,
                f'Successfully created voucher with code "{code}"',
            )
        except IntegrityError:
            messages.error(self.request, f'Voucher with code "{code}" already exist!')
            return None
        if form.cleaned_data['barter_value']:
            self._create_barter_deals(form, [voucher])
        if form.cleaned_data['delivery_discount']:
            self._create_delivery_discount(form, [voucher])

        return voucher

    def _create_vouchers(self, form: PromocodeAddForm, codes: list) -> list[Voucher]:
        create_params = self._get_voucher_create_params(form)
        vouchers = []
        for code in codes:
            voucher = Voucher(code=code, **create_params)
            vouchers.append(voucher)
        created_vouchers = Voucher.objects.bulk_create(vouchers, batch_size=100)
        if form.cleaned_data['barter_value']:
            self._create_barter_deals(form, created_vouchers)
        if form.cleaned_data['delivery_discount']:
            self._create_delivery_discount(form, created_vouchers)
        return created_vouchers

    def _get_voucher_create_params(self, form: PromocodeAddForm) -> dict:
        uses = form.cleaned_data['uses']
        excluded_items_json = self._get_exclude_item_conditionals(form.cleaned_data)
        create_parameters = {
            'kind_of': int(form.cleaned_data['kind_of']),
            'creator': self.request.user,
            'start_date': date.today(),
            'end_date': self._get_end_date(form),
            'quantity': uses,
            'value': form.cleaned_data['value'],
            'amount_starts': form.cleaned_data['limit_lower'],
            'amount_limit': form.cleaned_data['limit_upper'],
            'origin': form.cleaned_data['origin'],
            'quantity_left': uses if uses >= 0 else 0,
            'notes': form.cleaned_data['notes'],
            'ignore_on_invoice': form.cleaned_data['ignore_discount_on_invoice'],
            'item_conditionals': excluded_items_json,
        }
        return create_parameters

    def _get_end_date(self, form: PromocodeAddForm):
        end_date = form.cleaned_data['end_date']
        if end_date is not None and (
            end_date.hour,
            end_date.minute,
            end_date.second,
        ) == (0, 0, 0):
            end_date = end_date.replace(hour=23, minute=59, second=59)
        return end_date

    def _generate_codes(self, form: PromocodeAddForm, how_many: int) -> list:
        codes = []
        characters = form.cleaned_data['characters']
        letters_digits = form.cleaned_data['code_characters']

        for _ in range(how_many):
            try:
                code = Voucher.generate_code(
                    inside_string=form.cleaned_data['code'],
                    inside_string_at_beginning=True,
                    letters=letters_digits != 'digits',
                    digits=letters_digits != 'letters',
                    character_count=characters,
                    codes=codes,
                )
            except VoucherCodeUniquenessError as e:
                messages.error(self.request, e)
                return []

            codes.append(code)
        return codes

    @staticmethod
    def _get_exclude_item_conditionals(form_cleaned_data):
        shelf_type_color_map = {
            '0': 'excluded_colors_type_01',
            '1': 'excluded_colors_type_02',
            '2': 'excluded_colors_veneer_type_01',
            '3': 'excluded_colors_type_03',
            '4': 'excluded_colors_type_13',
        }

        exclude_items = []

        if form_cleaned_data['exclude_sample_box']:
            exclude_items.append({'furniture_types': [Furniture.sample_box.value]})

        for shelf_type in form_cleaned_data['excluded_furniture']:
            exclude_item = {'shelf_types': [shelf_type]}

            shelf_type_colors_field = shelf_type_color_map.get(shelf_type)
            if shelf_type_colors := form_cleaned_data.get(shelf_type_colors_field):
                exclude_item.update({'materials': shelf_type_colors})

            exclude_items.append(exclude_item)
        return {'exclude': exclude_items}


class RegionPriceCalculatorView(CustomerServiceMixinView):
    template_name = 'admin_custom/calculator.html'

    def get(self, request, *args, **kwargs):
        context = self.get_context_data(**kwargs)
        context['form'] = RegionCalculatorForm()
        return self.render_to_response(context)

    def post(self, request, *args, **kwargs):
        form = RegionCalculatorForm(request.POST)
        context = self.get_context_data(**kwargs)
        if form.is_valid():
            euro_price_brutto = form.cleaned_data['price_brutto']
            local_price_brutto = form.cleaned_data['regionalized_price_brutto']
            saved_currency = form.cleaned_data['currency']
        else:
            context['form'] = form
            return self.render_to_response(context)
        rate = saved_currency.rates.first()
        if rate is None:
            rate = 1
        else:
            rate = rate.rate
        quantize_kwargs = {'exp': Decimal('.01'), 'rounding': ROUND_HALF_UP}
        if euro_price_brutto:
            euro_price_brutto = Decimal(euro_price_brutto)
            local_price_brutto = euro_price_brutto * Decimal(rate)
        else:
            local_price_brutto = Decimal(local_price_brutto)
            euro_price_brutto = old_div(local_price_brutto, Decimal(rate))

        euro_price_brutto = euro_price_brutto.quantize(**quantize_kwargs)
        local_price_brutto = local_price_brutto.quantize(**quantize_kwargs)

        if form.cleaned_data['vat_type'] != '0':
            euro_price_netto = Decimal(euro_price_brutto)
            local_price_netto = Decimal(local_price_brutto)
        else:
            euro_price_netto = euro_price_brutto - (
                old_div(euro_price_brutto, Decimal(1.23))
            ) * Decimal(0.23)
            local_price_netto = local_price_brutto - (
                old_div(local_price_brutto, Decimal(1.23))
            ) * Decimal(0.23)

        context['form'] = form
        context['kwoty'] = {
            'euro_brutto': euro_price_brutto,
            'local_brutto': local_price_brutto,
            'euro_netto': euro_price_netto.quantize(**quantize_kwargs),
            'local_netto': local_price_netto.quantize(**quantize_kwargs),
            'local_currency': saved_currency.symbol,
        }
        return self.render_to_response(context)


class EditProformaView(CustomerServiceMixinView, TemplateView):
    """
    not so nice rewrite of a normal FormView,
    later will try to make a reusable Mixin out of it
    """

    template_name = 'customer_service/edit_proforma.html'
    forms = {
        'proforma_form': ProformaForm,
        'order_form': OrderForm,
        'invoice_address_form': OrderInvoiceAddressForm,
        'items_formset': InvoiceItemsFormSet,
    }

    def get_success_url(self):
        messages.info(self.request, 'Proforma saved!')
        return reverse_lazy(
            'cs_user_overview',
            args=[self.invoice.order.owner_id],
        )

    def get(self, request, *args, **kwargs):
        invoice = Invoice.objects.get(pk=self.kwargs['pk'])
        self.set_initials(invoice=invoice)
        return self.render_to_response(self.get_context_data())

    def post(self, request, *args, **kwargs):
        invoice = Invoice.objects.get(pk=self.kwargs['pk'])
        self.set_initials(invoice=invoice)
        if 'add_row' in request.POST:
            post = request.POST.copy()
            total_forms = int(post['items_formset-TOTAL_FORMS'])
            post['items_formset-TOTAL_FORMS'] = total_forms + 1
            request.POST = post
            return self.render_to_response(self.get_context_data())

        forms = self.get_forms()
        error_exist = False
        for name, form in list(forms.items()):
            if not form.is_valid():
                error_exist = True
        if error_exist:
            return self.form_invalid(forms)
        return self.form_valid(forms)

    def form_valid(self, forms):
        for name, form in list(forms.items()):
            form.save()
        self.invoice.proforma_sum_items()
        self.invoice.save()
        self.invoice.create_pdf()

        data = self.prepare_form_data(forms['proforma_form'].cleaned_data)
        self.log_cs_data(
            self.request,
            'cs_edit_proforma',
            self.invoice._meta.label,
            self.invoice.pk,
            data,
            additional_info=None,
        )
        return HttpResponseRedirect(self.get_success_url())

    def form_invalid(self, forms):
        return self.render_to_response(self.get_context_data(forms=forms))

    def get_forms(self):
        forms = {}
        for name, form_class in list(self.forms.items()):
            forms[name] = form_class(**self.get_form_kwargs(name))
        return forms

    def set_initials(self, **kwargs):
        invoice = kwargs['invoice']
        self.invoice = invoice
        self.instances = {
            'proforma_form': invoice,
            'order_form': invoice.order,
            'invoice_address_form': invoice.order,
            'items_formset': invoice,
        }

    def get_form_kwargs(self, name):
        kwargs = {}
        kwargs.update(
            {
                'instance': self.instances[name],
                'prefix': name,
            }
        )
        if self.request.method in ('POST', 'PUT'):
            kwargs.update(
                {
                    'data': self.request.POST,
                }
            )
        return kwargs

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['invoice'] = self.invoice
        if 'forms' not in kwargs:
            context['forms'] = self.get_forms()
        else:
            context['forms'] = kwargs['forms']
        return context


class AddSampleBoxView(CustomerServiceMixinView):
    _form_class = AddSampleBoxForm

    def post(self, request, *args, **kwargs):
        http_referer = request.META.get('HTTP_REFERER', None)
        form = self._form_class(request.POST)
        if form.is_valid():
            order_to_add = Order.objects.get(pk=form.cleaned_data['order_id'])
            sample_to_add = SampleBox()
            sample_to_add.owner = order_to_add.owner
            sample_to_add.box_variant = form.cleaned_data['variant']

            # TODO: this approach works for now as
            #       all sample boxes have the same preview
            sample_to_add.preview = SampleBox.objects.first().preview

            if order_to_add.status == OrderStatus.CART:
                sample_to_add.furniture_status = FurnitureStatusEnum.DRAFT
            else:
                sample_to_add.furniture_status = FurnitureStatusEnum.ORDERED
            sample_to_add.save()
            added_item = order_to_add.add_item(sample_to_add)

            OrderItemPriceCalculator(added_item).calculate()
            OrderPriceCalculator(order_to_add).calculate(recalculate_items=False)

            data = self.prepare_form_data(form.cleaned_data)
            self.log_cs_data(
                request,
                'cs_add_sample_box',
                order_to_add._meta.label,
                order_to_add.pk,
                data,
                additional_info=None,
            )
            messages.info(self.request, 'Done!')
            return redirect(http_referer)
        error_message = 'Oh no! Errors when adding sample box: {}'.format(
            form.errors,
        )
        messages.info(self.request, mark_safe(error_message))
        return redirect(http_referer)


class UnsuccessfulPaymentsView(CustomerServiceMixinView, ListView):
    template_name = 'customer_service/unsuccessful_payments.html'
    LIMIT_TO = 50

    def get_queryset(self):
        queryset = Order.objects.select_related(
            'owner',
            'owner__profile',
            'region',
        )
        queryset = queryset.prefetch_related(
            'items',
            'region__countries',
        )
        queryset = queryset.filter(
            status=OrderStatus.PAYMENT_PENDING,
            csunsuccessfulpayments__status=CSUnsuccessfulPaymentsStatus.NEW,
        )
        return queryset.order_by('-id')[: self.LIMIT_TO]


class UpdateOrderNotesView(CustomerServiceMixinView, View):
    def post(self, request, pk):
        order = get_object_or_404(Order, pk=pk)
        update_order_and_product_notes_from_cs_notes(order, request.POST.get('text'))
        return HttpResponse('OK')


class CustomerReviewsView(CustomerServiceMixinView, TemplateView):
    template_name = 'customer_service/reviews.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['tp_reviews'] = TrustPilotReviews(settings.TRUSTPILOT_ID).get_all()
        context['ts_reviews'] = TrustedShopReviews(
            settings.TRUSTEDSHOPS_TS_ID,
            settings.TRUSTEDSHOPS_USER,
            settings.TRUSTEDSHOPS_PASSWORD,
        ).get_all()
        return context


class PushToProductionView(SuccessMessageMixin, CustomerServiceMixinView, View):
    def get(self, request, pk=None):
        if pk is None:
            return redirect(reverse('cs_dashboard'))
        order = Order.objects.get(pk=pk)
        order.move_to_production_service.move()
        messages.info(request, f'Order {order.id} pushed to production')
        self.log_cs_data(
            request,
            'cs_push_to_production',
            order._meta.label,
            order.pk,
            {'previous_status': order.status},
            additional_info=None,
        )
        return redirect(
            request.META.get('HTTP_REFERER', reverse('cs_dashboard')),
        )


class MarginCalculatorView(CustomerServiceMixinView, TemplateView):
    template_name = 'customer_service/margin_calculator.html'


class GenerateInstructionsView(SuccessMessageMixin, CustomerServiceMixinView, View):
    def get(self, request, pk=None):
        if pk is None:
            return redirect(reverse('cs_dashboard'))
        order_item = OrderItem.objects.get(pk=pk)
        product = order_item.product_set.first()
        generate_instruction_task.delay(product.id)
        messages.info(
            request, f'Instructions have been generated for product {product.id}'
        )
        self.log_cs_data(
            request,
            'cs_generate_instructions',
            product._meta.label,
            product.pk,
            data={},
            additional_info={},
        )
        return redirect(
            request.META.get('HTTP_REFERER', reverse('cs_dashboard')),
        )


class WizardMixinView:
    MESSAGE_ERROR_STATUS = 'error'

    def should_redirect(self, step, request_url_name):
        return step != request_url_name

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['order'] = get_object_or_404(Order, pk=self.kwargs.get('order_id'))
        return context

    def dispatch(self, request, *args, **kwargs):
        order = get_object_or_404(Order, pk=self.kwargs.get('order_id'))
        step = self.get_step(order)
        if self.should_redirect(step, request.resolver_match.url_name):
            return redirect(reverse(step, kwargs={'order_id': order.pk}))
        return super().dispatch(request, *args, **kwargs)


class OrderSwitchWizardView(WizardMixinView, TemplateView):
    STATUS_TO_VIEW = {
        SwitchStatus.BLANK.value: 'cs_order_switch_item_on_hold',
        SwitchStatus.ITEM_REPLACEMENT.value: 'cs_order_switch_item_replacement',
        SwitchStatus.COST_RECALCULATION.value: 'cs_order_switch_order_recalculations',
        SwitchStatus.WAITING_FOR_ADDITIONAL_PAYMENT.value: 'cs_order_switch_commit_additional_payment',  # noqa: E501
        SwitchStatus.COMPLETED.value: 'cs_order_switch_summary_view',
        SwitchStatus.CANCELLED.value: 'cs_order_switch_item_on_hold',
    }

    def get_step(self, order: 'Order') -> str:
        return self.STATUS_TO_VIEW[order.switch_status]


class OrderSwitchItemOnHoldView(
    OrderSwitchLogCustomerServiceViewMixin, CustomerServiceMixinView, FormView
):
    STEP_NAME = 'cs_order_switch_item_on_hold'

    success_url = 'cs_order_switch_item_replacement'
    form_class = OrderSwitchItemOnHoldForm

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        order = get_object_or_404(Order, pk=self.kwargs.get('order_id'))
        context['order'] = order
        return context

    def get_success_url(self):
        return reverse_lazy(
            self.success_url, kwargs={'order_id': self.kwargs.get('order_id')}
        )

    def form_valid(self, form):
        order = get_object_or_404(Order, pk=self.kwargs.get('order_id'))
        source_order_item = get_object_or_404(
            OrderItem, pk=form.cleaned_data['source_order_item']
        )
        order.commit_item_on_hold(source_order_item, by=self.request.user)
        order.save()
        self.log_cs_order_switch_status_change(
            order, {'source_order_item': source_order_item.pk}
        )
        self.display_message(order, source_order_item)
        return super().form_valid(form)

    def display_message(self, order, source_order_item):
        messages.success(
            self.request,
            f'Product for {source_order_item} successfully changed status to on hold',
        )

        if is_order_promo_inactive(order):
            messages.error(
                self.request,
                f'Promocode: {order.used_promo.code} which is not active anymore '
                'was applied to this order. Please activate this coupon for purpose '
                'of this switch or handle it manually',
            )


class OrderSwitchItemReplacementView(
    OrderSwitchLogCustomerServiceViewMixin,
    OrderSwitchWizardView,
    CustomerServiceMixinView,
    FormView,
):
    STEP_NAME = 'cs_order_switch_item_replacement'

    template_name = 'customer_service/order_switch/item_replacement.html'
    form_class = OrderSwitchItemReplacementForm

    def get_form_kwargs(self):
        form_kwargs = super().get_form_kwargs()
        self.order = get_object_or_404(Order, pk=self.kwargs.get('order_id'))
        form_kwargs['source_order_item'] = self.order.source_order_item
        return form_kwargs

    def get_success_url(self):
        return reverse_lazy(
            'cs_order_switch_order_recalculations',
            kwargs={'order_id': self.kwargs.get('order_id')},
        )

    def form_valid(self, form):
        furniture_to_add = form.cleaned_data['furniture_to_add']
        quantity = form.cleaned_data['quantity']

        message_status, message_text = self.order.commit_item_replacement(
            furniture_to_add,
            quantity=quantity,
            by=self.request.user,
        )
        self.order.save()
        self.log_cs_order_switch_status_change(
            self.order, {'furniture_to_add': furniture_to_add.pk}
        )
        self.display_message(message_status, message_text)
        return super().form_valid(form)

    def display_message(self, message_status, message_text):
        if message_status == self.MESSAGE_ERROR_STATUS:
            messages.error(self.request, message_text)
        else:
            messages.success(self.request, 'Please check price differences')


class OrderSwitchRecalculationsView(
    OrderSwitchLogCustomerServiceViewMixin,
    OrderSwitchWizardView,
    CustomerServiceMixinView,
    FormView,
):
    STEP_NAME = 'cs_order_switch_order_recalculations'

    template_name = 'customer_service/order_switch/order_recalculations.html'
    form_class = OrderSwitchRecalculationsForm

    def get_form_kwargs(self):
        form_kwargs = super().get_form_kwargs()
        self.order = get_object_or_404(Order, pk=self.kwargs.get('order_id'))
        form_kwargs['order'] = self.order
        return form_kwargs

    def get_success_url(self):
        order = get_object_or_404(Order, pk=self.kwargs.get('order_id'))
        kwargs = {'order_id': self.kwargs.get('order_id')}
        if order.should_wait_for_additional_payment():
            return reverse('cs_order_switch_commit_additional_payment', kwargs=kwargs)
        return reverse('cs_order_switch_summary_view', kwargs=kwargs)

    def form_valid(self, form):
        order = get_object_or_404(Order, pk=self.kwargs.get('order_id'))
        options = form.cleaned_data
        order.commit_recalculations(options, by=self.request.user, description=options)
        order.save()
        self.log_cs_order_switch_status_change(self.order, options)
        self.display_message(order)
        return super().form_valid(form)

    def display_message(self, order):
        if order.message_status == self.MESSAGE_ERROR_STATUS:
            messages.error(self.request, order.message_text)
        elif order.should_wait_for_additional_payment():
            messages.info(
                self.request,
                f'Waiting for additional payment of {order.total_price_change} '
                f'product for {order.target_order_item} successfully changed '
                'status to on hold',
            )
        else:
            messages.success(self.request, 'Project has been changed')


class OrderSwitchWaitingForAdditionalPaymentsView(
    OrderSwitchLogCustomerServiceViewMixin,
    OrderSwitchWizardView,
    CustomerServiceMixinView,
):
    STEP_NAME = 'cs_order_switch_commit_additional_payment'

    template_name = 'customer_service/order_switch/waiting_for_additional_payments.html'

    def post(self, request, *args, **kwargs):
        order = get_object_or_404(Order, pk=self.kwargs.get('order_id'))

        order.commit_additional_payment(by=self.request.user)
        order.save(update_fields=['switch_status'])
        self.log_cs_order_switch_status_change(order, kwargs)

        return redirect(
            reverse_lazy('cs_order_switch_summary_view', kwargs={'order_id': order.id})
        )


class OrderSwitchRollbackItemOnHoldView(CustomerServiceMixinView):
    def post(self, request, *args, **kwargs):
        order = get_object_or_404(Order, pk=self.kwargs.get('order_id'))
        order.rollback_item_on_hold(by=request.user)
        order.save()
        messages.success(request, 'Product successfully changed status back to normal')
        return redirect(reverse_lazy('cs_user_overview', args=[order.owner_id]))


class OrderSwitchRollbackItemReplacementView(CustomerServiceMixinView):
    def post(self, request, *args, **kwargs):
        order = get_object_or_404(Order, pk=self.kwargs.get('order_id'))
        order.rollback_item_replacement(by=request.user)
        order.save()
        messages.success(
            request, 'Switch successfully changed to Item Replacement step'
        )

        return redirect(
            reverse(
                'cs_order_switch_item_replacement',
                kwargs={'order_id': self.kwargs.get('order_id')},
            )
        )


class OrderSwitchRollbackItemReplacementAndItemOnHoldView(CustomerServiceMixinView):
    @transaction.atomic
    def post(self, request, *args, **kwargs):
        order = get_object_or_404(Order, pk=self.kwargs.get('order_id'))
        order.rollback_item_replacement(by=request.user)
        order.rollback_item_on_hold(by=request.user)
        order.save()
        messages.success(request, 'Product successfully changed status back to normal')
        return redirect(reverse_lazy('cs_user_overview', args=[order.owner_id]))


class OrderSwitchSummaryView(OrderSwitchWizardView, CustomerServiceMixinView):
    template_name = 'customer_service/order_switch/summary.html'


class AbortOrderView(SuccessMessageMixin, CustomerServiceMixinView):
    def post(self, request, pk):
        product = get_object_or_404(Product, pk=pk)
        if product.order_item.pending_abort_request is not None:
            messages.error(
                request,
                "You can't abort when Order has pending abort request",
            )
            return redirect(
                reverse('cs_user_overview', args=[product.order.owner_id]),
            )

        if product.order.has_pending_correction_request:
            messages.error(
                request,
                "You can't abort when Order has pending correcting request",
            )
            return redirect(
                reverse('cs_user_overview', args=[product.order.owner_id]),
            )
        order_items_with_quantity = [
            {
                'order_item_id': product.order_item.id,
                'quantity': 1,
            }
        ]
        request_order_abort(
            order=product.order,
            order_items_with_quantity=order_items_with_quantity,
            user=request.user,
        )

        return redirect(
            reverse('cs_user_overview', args=[product.order.owner_id]),
        )


class ChangeIsBusinessTypeView(
    SuccessMessageMixin, UpdateView, CustomerServiceMixinView
):
    form_class = UserProfileIsBusinessTypeForm
    model = UserProfile

    def get_success_url(self):
        return reverse('cs_user_overview', args=[self.object.user.pk])


class CustomerContactView(CustomerServiceMixinView, ListView):
    model = CustomerContact
    template_name = 'customer_service/customer_contact.html'
    paginate_by = 100

    def get_queryset(self):
        return CustomerContact.objects.prefetch_related('images').order_by('-id')


class CustomerContactDetailView(CustomerServiceMixinView, DetailView):
    model = CustomerContact
    template_name = 'customer_service/customer_contact_detail.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        context['damaged_elements_images'] = self.object.images.filter(
            image_type=ImageType.DAMAGED_ELEMENTS.value
        )
        context['damaged_package_images'] = self.object.images.filter(
            image_type=ImageType.DAMAGED_PACKAGE.value
        )

        return context


class DeactivateUserAccountView(CustomerServiceMixinView, UpdateView):
    form_class = DeactivationUserForm
    model = UserProfile
    template_name = 'customer_service/user_deactivate.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        user = self.object.user
        orders = Order.objects.filter(owner_id=user.id)
        context['orders'] = orders
        context['invoices'] = Invoice.objects.filter(order__in=orders)
        return context

    def get_success_url(self):
        return reverse('cs_user_overview', args=[self.object.user_id])


class NewsletterUnsubscribeFormView(FormView):
    template_name = 'customer_service/unsubscribe_from_newsletter.html'
    form_class = NewsletterUnsubscribeForm

    def get_success_url(self):
        return reverse('cs_dashboard')

    def form_valid(self, form):
        email = form.cleaned_data['email']
        user = self._get_user(email)

        if not user:
            return super().form_valid(form)

        subscription_groups = []
        if form.cleaned_data['unsubscribe_from_newsletter']:
            subscription_groups.append(
                settings.BRAZE_EMAIL_SUBSCRIPTION_GROUPS['newsletter'],
            )
        if form.cleaned_data['unsubscribe_from_saved_item_flow']:
            subscription_groups.append(
                settings.BRAZE_EMAIL_SUBSCRIPTION_GROUPS['save_for_later']
            )

        for subscription_group in subscription_groups:
            EmailSubscriptionEvent(
                user=user,
                subscription_group=subscription_group,
                subscription_state=BrazeSubscriptionStates.UNSUBSCRIBED,
                email=email,
            )

        messages.success(self.request, 'Unsubscribed.')
        return super().form_valid(form)

    def _get_user(self, email: str) -> Optional[User]:
        user = User.objects.filter(email=email).last()
        if not user:
            user_profile = UserProfile.objects.filter(email=email).last()
            if user_profile:
                return user_profile.user

            message = (
                'There is no user with such email. '
                'Contact CRM team to check subscription info in Braze directly.'
            )
            messages.error(self.request, message)

        return user


class CreateKlarnaAdjustmentView(
    SuccessMessageMixin,
    CreateView,
    CustomerServiceMixinView,
):
    template_name = 'customer_service/create_klarna_adjustment.html'
    form_class = KlarnaAdjustmentCreateForm
    success_message = 'Klarna adjustment created'

    def get_success_url(self):
        return reverse('cs_user_overview', args=[self.object.invoice.order.owner_id])

    def get_initial(self):
        return {'invoice': self.kwargs['invoice_id']}


class OrderAddExtraDiscountWizardView(WizardMixinView, TemplateView):
    STATUS_TO_VIEW = {
        ExtraDiscountStatus.BLANK.value: 'cs_order_add_extra_discount_recalculation',
        ExtraDiscountStatus.DISCOUNT_RECALCULATION.value: 'cs_order_add_extra_discount_summary',  # noqa: E501
        ExtraDiscountStatus.COMPLETED.value: 'cs_order_add_extra_discount_recalculation',  # noqa: E501
        ExtraDiscountStatus.CANCELLED.value: 'cs_order_add_extra_discount_recalculation',  # noqa: E501
    }

    def get_step(self, order: 'Order') -> str:
        return self.STATUS_TO_VIEW[order.extra_discount_status]


class OrderAddExtraDiscountRecalculationsView(
    OrderAddExtraDiscountWizardView,
    CustomerServiceMixinView,
    FormView,
):

    template_name = 'customer_service/order_extra_discount/voucher_replacement.html'
    form_class = OrderAddExtraDiscountVoucherReplacementForm

    def get_form_kwargs(self):
        form_kwargs = super().get_form_kwargs()
        self.order = get_object_or_404(Order, pk=self.kwargs.get('order_id'))
        form_kwargs['source_voucher'] = self.order.used_promo
        return form_kwargs

    def get_success_url(self):
        return reverse_lazy(
            'cs_order_add_extra_discount_summary',
            kwargs={'order_id': self.kwargs.get('order_id')},
        )

    def form_valid(self, form):
        target_voucher = form.cleaned_data['target_voucher']

        message_status, message_text = self.order.commit_discount_recalculation(
            target_voucher,
            by=self.request.user,
        )
        self.order.save()
        self.display_message(message_status, message_text)
        return super().form_valid(form)

    def display_message(self, message_status, message_text):
        if message_status == self.MESSAGE_ERROR_STATUS:
            messages.error(self.request, message_text)
        else:
            messages.success(self.request, 'Please check price differences')


class OrderAddExtraDiscountRollbackRecalculationsView(CustomerServiceMixinView):
    def post(self, request, *args, **kwargs):
        order = get_object_or_404(Order, pk=self.kwargs.get('order_id'))
        order.rollback_discount_recalculation(by=request.user)
        order.save()
        messages.success(
            request, 'Extra discount successfully changed to Recalculations step'
        )

        return redirect(
            reverse(
                'cs_order_add_extra_discount_recalculation',
                kwargs={'order_id': self.kwargs.get('order_id')},
            )
        )


class OrderAddExtraDiscountSummaryView(
    OrderAddExtraDiscountWizardView,
    CustomerServiceMixinView,
):
    KLARNA_SUCCESS_MESSAGE = 'Klarna Adjustment ID: {} has been created'
    CORRECTION_REQUEST_SUCCESS_MESSAGE = 'CSCorrectionRequest ID: {} has been created'

    template_name = 'customer_service/order_extra_discount/summary.html'

    def post(self, request, *args, **kwargs):
        order = get_object_or_404(Order, pk=self.kwargs.get('order_id'))

        klarna_adjustment_or_correction_request = order.commit_completed(
            by=self.request.user
        )
        self.display_message(order, klarna_adjustment_or_correction_request)
        order.save(update_fields=['extra_discount_status'])
        return redirect(
            reverse('cs_user_overview', args=[order.owner_id]),
        )

    def display_message(self, order, klarna_adjustment_or_correction_request):
        if order.is_klarna_with_proforma_only():
            message_text = self.KLARNA_SUCCESS_MESSAGE
        else:
            message_text = self.CORRECTION_REQUEST_SUCCESS_MESSAGE

        messages.success(
            self.request,
            message_text.format(klarna_adjustment_or_correction_request.pk),
        )
