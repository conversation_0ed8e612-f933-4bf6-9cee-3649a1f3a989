from celery.schedules import crontab

customer_service_tasks_scheduler = {
    'cs_pending_failure_report': {
        'task': 'customer_service.tasks.cs_pending_failure_report',
        'schedule': crontab(minute='10'),
    },
    'cs_pending_failure_clean_report': {
        'task': 'customer_service.tasks.cs_pending_failure_clean_report',
        'schedule': crontab(minute='15'),
    },
    'process_pending_order_abort_requests': {
        'task': 'customer_service.tasks.process_pending_order_abort_requests',
        'schedule': crontab(minute='*/5'),
    },
}

customer_service_tasks_scheduler_dev = {
    'process_pending_order_abort_requests': {
        'task': 'customer_service.tasks.process_pending_order_abort_requests',
        'schedule': crontab(minute='*/5'),
    },
}
