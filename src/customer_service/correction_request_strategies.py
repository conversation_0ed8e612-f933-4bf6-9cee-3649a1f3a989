import logging
import typing

from decimal import Decimal

from django.conf import settings
from django.utils import timezone
from django.utils.translation import gettext as _

from past.utils import old_div

from accounting.enums import CashBackTypeChoices
from customer_service.enums import (
    CSCorrectionRequestStatus,
    CSCorrectionRequestType,
)
from events.choices import MailingInvoiceTypeChoices
from events.domain_events.transact_events import InvoiceReadyEvent
from invoice.choices import (
    InvoiceChangeScope,
    InvoiceItemType,
    InvoiceStatus,
    VatType,
)
from invoice.constants import VAT_CHANGE_DATETIME_BY_COUNTRY
from invoice.enums import InvoiceItemTag
from invoice.models import (
    Invoice,
    InvoiceCorrectionChange,
)
from invoice.symfonia import generate_date_for_symfonia_export
from invoice.utils import (
    get_corrected_invoice_item_by_type_and_order_item,
    update_invoice_items_and_corrected_notes,
)
from mailing.templates import (
    OrderCorrectionInvoiceFreeReturnKlarnaMail,
    OrderCorrectionInvoiceFreeReturnNormalMail,
    OrderCorrectionInvoiceMail,
)
from regions.models import Currency

if typing.TYPE_CHECKING:
    from django.contrib.auth import get_user_model

    from customer_service.models import CSCorrectionRequest

    User = get_user_model()

logger = logging.getLogger('cstm')


def get_correction_request_strategy(correction_request):
    CORRECTION_REQUEST_TYPE_TO_STRATEGY = {
        CSCorrectionRequestType.TYPE_AMOUNT_VAT: CorrectionRequestAmountStrategy,
        CSCorrectionRequestType.TYPE_EXTRA_DISCOUNT: CorrectionRequestAmountStrategy,
        CSCorrectionRequestType.TYPE_ADDRESS: CorrectionRequestAddressStrategy,
        CSCorrectionRequestType.TYPE_NEUTRALIZATION: CorrectionRequestNeutralizationStrategy,  # noqa: E501
        CSCorrectionRequestType.TYPE_SWITCH: CorrectionRequestSwitchStrategy,
        CSCorrectionRequestType.TYPE_FREE_RETURN: CorrectionRequestFreeReturnStrategy,
        CSCorrectionRequestType.TYPE_ITEMS: CorrectionRequestQuantityStrategy,
    }

    strategy = CORRECTION_REQUEST_TYPE_TO_STRATEGY.get(correction_request.type_cs)
    return strategy(correction_request)


def add_tag_to_invoice_item(correction_request, correction_invoice):
    tag = InvoiceItemTag.cs_correction_status_to_invoice_tag_mapper(
        correction_request.type_cs
    )
    if correction_request.type_cs in [
        CSCorrectionRequestType.TYPE_AMOUNT_VAT,
        CSCorrectionRequestType.TYPE_NEUTRALIZATION,
        CSCorrectionRequestType.TYPE_EXTRA_DISCOUNT,
    ]:
        tag = correction_request.tag
    tag = tag or correction_request.tag
    update_invoice_items_and_corrected_notes(correction_invoice, tag)
    return correction_invoice


def set_additional_total_when_united_kingdom_lte_tax_threshold(
    correction_invoice: 'Invoice',
) -> None:
    if correction_invoice.order.is_united_kingdom_lte_tax_threshold(
        correction_invoice.region_total_value
    ):
        correction_invoice.set_additional_total_when_united_kingdom_lte_tax_threshold(
            correction_invoice.region_total_value
        )


class AbstractCorrectionRequestStrategy:
    def __init__(self, correction_request):
        self.correction_request = correction_request

    def _set_up_correction(self, issued_at=None):
        correction_request = self.correction_request
        if correction_request.correction_vat:
            raise ValueError('Unsupported correction_vat')

        source_invoice = correction_request.invoice
        correction_invoice = Invoice()
        # first delete all old drafts for this order,
        Invoice.objects.filter(
            status=InvoiceStatus.CORRECTING_DRAFT, order=source_invoice.order
        ).delete()
        correction_invoice.issued_at = issued_at if issued_at else timezone.now()
        if source_invoice.corrected_invoice is not None:
            correction_invoice.corrected_invoice = source_invoice.corrected_invoice
        else:
            correction_invoice.corrected_invoice = source_invoice
        correction_invoice.sell_at = source_invoice.sell_at
        correction_invoice.currency_symbol = source_invoice.currency_symbol
        correction_invoice.status = InvoiceStatus.CORRECTING_DRAFT
        correction_invoice.additional_address_1 = source_invoice.additional_address_1
        correction_invoice.additional_address_2 = source_invoice.additional_address_2
        correction_invoice.order = source_invoice.order
        correction_invoice.save()

        return correction_invoice, source_invoice

    def _finalize_correction(
        self, correction_request: 'CSCorrectionRequest', user: 'User', send_email: bool
    ) -> 'Invoice':
        from accounting.models import MoneyCashBack

        correction_invoice = correction_request.correction_invoice
        correction_invoice.to_dict(force_refresh=True)
        correction_invoice.create_pdf()

        send_email = False  # FIXME: Temporary, requested by accounting
        if (
            correction_invoice.order.is_united_kingdom()
            and not correction_invoice.is_domestic
        ):
            send_email = False

        if send_email:
            if settings.PROCESS_TRANSACT_FLOWS_ON_BE:
                self._send_email(correction_invoice)
            else:
                InvoiceReadyEvent(
                    user=correction_request.invoice.order.owner,
                    order_id=correction_request.invoice.order.id,
                    email=correction_request.invoice.order.email,
                    invoice_type=MailingInvoiceTypeChoices.CORRECTION,
                    invoice_url=correction_invoice.pdf.url,
                    is_klarna=correction_invoice.order.is_klarna_payment(),
                )

            correction_invoice.sent_invoice_at = timezone.now()
            correction_invoice.save(update_fields=('sent_invoice_at',))
        if user is not None:
            correction_request.reviewer = user
            correction_request.status = CSCorrectionRequestStatus.STATUS_ACCEPTED
            correction_request.save()

        diff = correction_invoice.to_diff_dict()
        if diff.get('total_value_in_pln', 0):
            symfonia_order, symfonia_client = generate_date_for_symfonia_export(
                correction_invoice
            )
            mb = MoneyCashBack(
                invoice=correction_invoice,
                amount=correction_request.correction_amount_gross * -1,
                cash_back_type=CashBackTypeChoices.CASH_BACK_OTHER,
                order=correction_invoice.order,
                client_name=symfonia_client['nazwa'],
                cash_back_type_other=correction_invoice.corrected_notes,
                currency=Currency.objects.get(
                    symbol=correction_invoice.currency_symbol
                ),
            )
            mb.save()
        return correction_invoice

    def _send_email(self, correction_invoice):
        correction_request = self.correction_request

        mail_class = OrderCorrectionInvoiceMail
        if (
            hasattr(correction_request, 'free_return')
            and correction_request.type_cs
            in (
                CSCorrectionRequestType.TYPE_NEUTRALIZATION,
                CSCorrectionRequestType.TYPE_FREE_RETURN,
            )
            and correction_request.tag == InvoiceItemTag.FREE_RETURN.value
        ):
            mail_class = (
                OrderCorrectionInvoiceFreeReturnKlarnaMail
                if correction_invoice.order.is_klarna_payment()
                else OrderCorrectionInvoiceFreeReturnNormalMail
            )

            mail = mail_class(
                correction_invoice.order.email,
                {
                    'order': correction_invoice.order.order_pretty_id,
                    'invoice': correction_invoice,
                    'user': correction_invoice.order.owner.first_name,
                    'order_items_id': ', '.join(
                        str(item_id)
                        for item_id in correction_invoice.order.items.filter(
                            free_return=correction_request.free_return
                        ).values_list('id', flat=True)
                    ),
                },
                files_to_add=[correction_invoice.pdf],
                topic_variables={
                    'order': correction_invoice.order.order_pretty_id,
                    'order_pretty_id': correction_invoice.order.order_pretty_id,
                },
            )
        else:
            mail = mail_class(
                correction_invoice.order.email,
                {'order': correction_invoice.order, 'invoice': correction_invoice},
                files_to_add=[correction_invoice.pdf],
                topic_variables={
                    'order_pretty_id': correction_invoice.order.order_pretty_id
                },
            )
        mail.send(correction_invoice.order.owner.profile.language)
        correction_invoice.sent_invoice_at = timezone.now()
        correction_invoice.save()

    @classmethod
    def is_country_before_vat_change_for_additional_payment(
        cls, correction_request: 'CSCorrectionRequest'
    ) -> bool:
        country = correction_request.correction_invoice.order.country
        is_changed_vat_country = country.lower() in VAT_CHANGE_DATETIME_BY_COUNTRY
        if not is_changed_vat_country:
            return False

        client_should_pay_more = correction_request.correction_amount_gross < Decimal(
            '0.0'
        )
        sell_at_before_change = (
            correction_request.invoice.sell_at
            < VAT_CHANGE_DATETIME_BY_COUNTRY[country.lower()]
        )
        return client_should_pay_more and sell_at_before_change

    def accept(self, user, issued_at=None):
        if self.is_country_before_vat_change_for_additional_payment(
            self.correction_request
        ):
            country = self.correction_request.correction_invoice.order.country

            raise ValueError(
                f'This request is for {country} '
                f'when client should pay more and previous invoice was with different '
                f"tax rate, let's ask OP4 to handle it manually"
            )

        correction_request = self.correction_request
        correction_invoice = correction_request.correction_invoice

        issued_at = issued_at or timezone.now()
        correction_invoice.status = InvoiceStatus.CORRECTING
        correction_invoice.issued_at = issued_at
        correction_invoice.pretty_id = correction_invoice._generate_pretty_id(
            issued_at=issued_at
        )
        return self._finalize_correction(correction_request, user=user, send_email=True)


class CorrectionRequestSwitchStrategy(AbstractCorrectionRequestStrategy):
    def prepare_correcting_draft_for_switch_with_absolute_voucher(self, target_invoice):
        correction_request = self.correction_request

        correction_invoice, source_invoice = self._set_up_correction()

        self._copy_target_invoice_items_without_added(
            correction_invoice, source_invoice, target_invoice
        )
        correction_added_invoice_items = self._set_added_invoice_items_to_correction(
            correction_invoice
        )
        correction_request.added_invoice_items.set(correction_added_invoice_items)

        add_tag_to_invoice_item(correction_request, correction_invoice)
        set_additional_total_when_united_kingdom_lte_tax_threshold(correction_invoice)

        correction_request.correction_invoice = correction_invoice
        correction_request.correction_invoice.create_pdf()
        correction_request.save()
        return correction_invoice

    def _copy_target_invoice_items_without_added(
        self, correction_invoice, source_invoice, target_invoice
    ):
        correction_request = self.correction_request

        added_invoice_items_pks = correction_request.added_invoice_items.values_list(
            'pk', flat=True
        )
        target_invoice_items_without_added = target_invoice.invoice_items.exclude(
            pk__in=added_invoice_items_pks
        )

        for invoice_item in target_invoice_items_without_added:
            invoice_item.pk = None
            invoice_item.invoice_id = correction_invoice.id
            corrected_invoice_item = get_corrected_invoice_item_by_type_and_order_item(
                source_invoice, invoice_item
            )
            invoice_item.corrected_invoice_item_id = corrected_invoice_item.pk
            invoice_item.save()

    def prepare_correcting_draft_for_switch(self):
        correction_request = self.correction_request
        correction_invoice, source_invoice = self._set_up_correction()

        self._copy_source_invoice_items_without_deleted(
            correction_invoice, source_invoice
        )
        correction_added_invoice_items = self._set_added_invoice_items_to_correction(
            correction_invoice
        )
        correction_request.added_invoice_items.set(correction_added_invoice_items)
        add_tag_to_invoice_item(correction_request, correction_invoice)
        set_additional_total_when_united_kingdom_lte_tax_threshold(correction_invoice)

        correction_request.correction_invoice = correction_invoice
        correction_request.correction_invoice.create_pdf()
        correction_request.save()
        return correction_invoice

    def _set_added_invoice_items_to_correction(self, correction_invoice):
        correction_request = self.correction_request

        correction_added_invoice_items = []
        for invoice_item in correction_request.added_invoice_items.all():
            invoice_item.pk = None
            invoice_item.invoice_id = correction_invoice.id
            correct_invoice_item = correction_request.deleted_invoice_items.filter(
                item_type=invoice_item.item_type
            ).first()
            invoice_item.corrected_invoice_item_id = (
                correct_invoice_item.pk if correct_invoice_item else None
            )
            invoice_item.save()
            correction_added_invoice_items.append(invoice_item)
        return correction_added_invoice_items

    def _copy_source_invoice_items_without_deleted(
        self, correction_invoice, source_invoice
    ):
        correction_request = self.correction_request

        deleted_invoice_items_pks = (
            correction_request.deleted_invoice_items.values_list('pk', flat=True)
        )
        source_invoice_items_without_deleted = source_invoice.invoice_items.exclude(
            pk__in=deleted_invoice_items_pks
        )

        for invoice_item in source_invoice_items_without_deleted:
            old_pk = invoice_item.pk
            invoice_item.pk = None
            invoice_item.invoice_id = correction_invoice.id
            invoice_item.corrected_invoice_item_id = old_pk
            invoice_item.save()


class CorrectionRequestQuantityStrategy(AbstractCorrectionRequestStrategy):
    def prepare_correction_request(self) -> 'CSCorrectionRequest':
        correction_request = self.correction_request
        correction_invoice, original_invoice = self._set_up_correction()
        self._set_deleted_items_to_correction(original_invoice, correction_invoice)

        if correction_request.correction_context == InvoiceItemTag.FREE_RETURN:
            self._set_added_invoice_items_to_correction(correction_invoice)

        add_tag_to_invoice_item(correction_request, correction_invoice)
        set_additional_total_when_united_kingdom_lte_tax_threshold(correction_invoice)

        correction_request.correction_invoice = correction_invoice
        correction_request.correction_invoice.create_pdf()
        correction_request.save()
        return correction_request

    def _set_deleted_items_to_correction(self, original_invoice, correction_invoice):
        correction_request = self.correction_request
        deleted_invoice_items = correction_request.deleted_invoice_items.all()
        if correction_request.tag == InvoiceItemTag.FREE_RETURN:
            deleted_invoice_items = (
                correction_request.deleted_invoice_items.exclude(
                    item_type=InvoiceItemType.ASSEMBLY
                )
                .exclude(item_type=InvoiceItemType.DELIVERY)
                .exclude(item_type=InvoiceItemType.SERVICE)
            )

        deleted_invoice_items_ids = list(
            deleted_invoice_items.values_list('id', flat=True)
        )
        for invoice_item in original_invoice.invoice_items.all():
            old_pk = invoice_item.pk
            invoice_item.pk = None
            invoice_item.invoice_id = correction_invoice.id
            invoice_item.corrected_invoice_item_id = old_pk
            if old_pk in deleted_invoice_items_ids:
                invoice_item.quantity = 0
                invoice_item.gross_price = 0
                invoice_item.vat_amount = 0
                invoice_item.net_value = 0
                invoice_item.discount_value = 0
            invoice_item.save()

    def _set_added_invoice_items_to_correction(self, correction_invoice):
        correction_request = self.correction_request

        correction_added_invoice_items = []
        for invoice_item in correction_request.added_invoice_items.all():
            invoice_item.pk = None
            invoice_item.invoice_id = correction_invoice.id
            correct_invoice_item = correction_request.deleted_invoice_items.filter(
                item_type=invoice_item.item_type
            ).first()
            invoice_item.corrected_invoice_item_id = (
                correct_invoice_item.pk if correct_invoice_item else None
            )
            invoice_item.save()
            correction_added_invoice_items.append(invoice_item)
        return correction_added_invoice_items


class CorrectionRequestAddressStrategy(AbstractCorrectionRequestStrategy):
    def prepare_correction_request(self) -> 'CSCorrectionRequest':
        from customer_service.models import CSCorrectionAddressRequest

        correction_request = self.correction_request

        correction_invoice, source_invoice = self._set_up_correction()

        company = '<h5>{} <br/> {} {} </h5>'.format(
            correction_request.cs_correction_request.company_name,
            correction_request.cs_correction_request.first_name,
            correction_request.cs_correction_request.last_name,
        )
        private_customer = '<h5>{} {} </h5>'.format(
            correction_request.cs_correction_request.first_name,
            correction_request.cs_correction_request.last_name,
        )

        correction_invoice.additional_address_1 = (
            company
            if correction_request.cs_correction_request.company_name
            else private_customer
        )
        correction_invoice.additional_address_1 += '''<p>{} {} </p>
            <p>{} {} </p>
            <p>{} </p>'''.format(
            correction_request.cs_correction_request.street_address_1,
            correction_request.cs_correction_request.street_address_2
            if correction_request.cs_correction_request.street_address_2
            else '',
            correction_request.cs_correction_request.postal_code,
            correction_request.cs_correction_request.city,
            _(correction_request.cs_correction_request.country),
        )
        correction_invoice.additional_address_1 += (
            '<p>TAX: <em>{}</em></p>'.format(
                correction_request.cs_correction_request.vat
            )
            if correction_request.cs_correction_request.vat
            else ''
        )

        fields = [f.name for f in CSCorrectionAddressRequest._meta.fields]
        fields.remove('id')
        diff = {}
        for field in fields:
            if getattr(correction_request.invoice.order, field) != getattr(
                correction_request.cs_correction_request, field
            ):
                diff[field] = (
                    getattr(correction_request.invoice.order, field),
                    getattr(correction_request.cs_correction_request, field),
                )

        correction_change = InvoiceCorrectionChange()
        correction_change.correcting_invoice = correction_invoice
        correction_change.correction_type = InvoiceChangeScope.OTHER
        correction_change.name = 'Customer`s Address/ Adres kontrahenta'
        correction_change.previous_state = ''.join(
            [('<p>%s</p>' % (x[1][0] if x[1][0] else '')) for x in list(diff.items())]
        )
        correction_change.current_state = ''.join(
            [('<p>%s</p>' % (x[1][1] if x[1][1] else '')) for x in list(diff.items())]
        )
        correction_change.save()

        for invoice_item in source_invoice.invoice_items.all():
            old_pk = invoice_item.pk
            invoice_item.pk = None
            invoice_item.tag = InvoiceItemTag.CHANGE_OF_ADDRESS.value
            invoice_item.invoice_id = correction_invoice.id
            invoice_item.corrected_invoice_item_id = old_pk
            invoice_item.save()

        add_tag_to_invoice_item(correction_request, correction_invoice)
        set_additional_total_when_united_kingdom_lte_tax_threshold(correction_invoice)

        correction_request.correction_invoice = correction_invoice
        correction_request.correction_invoice.create_pdf()
        correction_request.save()
        return correction_request


class CorrectionRequestNeutralizationStrategy(AbstractCorrectionRequestStrategy):
    def prepare_correction_request(self, issued_at=None) -> 'CSCorrectionRequest':
        correction_request = self.correction_request
        correction_invoice, source_invoice = self._set_up_correction(
            issued_at=issued_at
        )

        for invoice_item in source_invoice.invoice_items.all():
            old_pk = invoice_item.pk
            invoice_item.pk = None
            invoice_item.invoice_id = correction_invoice.id
            invoice_item.corrected_invoice_item_id = old_pk
            invoice_item.quantity = 0
            invoice_item.net_price = Decimal('0.0')
            invoice_item.gross_price = Decimal('0.0')
            invoice_item.vat_amount = Decimal('0.0')
            invoice_item.net_value = Decimal('0.0')
            invoice_item.discount_value = Decimal('0.0')
            invoice_item.recycle_tax_value = Decimal('0.0')

            invoice_item.net_weight = Decimal('0.0')
            invoice_item.gross_weight = Decimal('0.0')
            invoice_item.save()

        add_tag_to_invoice_item(correction_request, correction_invoice)
        set_additional_total_when_united_kingdom_lte_tax_threshold(correction_invoice)

        correction_request.correction_invoice = correction_invoice
        correction_request.correction_invoice.create_pdf()
        correction_request.save()
        return correction_request


class CorrectionRequestAmountStrategy(AbstractCorrectionRequestStrategy):
    def prepare_correction_request(self, issued_at=None) -> 'CSCorrectionRequest':
        correction_request = self.correction_request
        correction_invoice, source_invoice = self._set_up_correction(
            issued_at=issued_at
        )

        invoice_items = source_invoice.filter_invoice_items()
        amount_to_remove = correction_request.correction_amount_gross

        if invoice_items.count() == 1:
            invoice_item = invoice_items.first()
            invoice_item = self.copy_invoice_item(correction_invoice, invoice_item)
            self._recalculate_invoice_item(amount_to_remove, invoice_item)
        else:
            correction_percentage = self.calculate_percentage_discount(
                correction_request.correction_amount_gross,
                invoice_items,
            )

            correction_amount_left = correction_request.correction_amount_gross
            last_item = None

            for invoice_item in invoice_items.all():
                invoice_item = self.copy_invoice_item(correction_invoice, invoice_item)

                amount_to_remove = (
                    correction_percentage * invoice_item.gross_price
                ).quantize(Decimal('.01'))
                self._recalculate_invoice_item(amount_to_remove, invoice_item)
                correction_amount_left -= amount_to_remove
                last_item = invoice_item

            if correction_amount_left != 0:
                amount_to_remove = correction_amount_left
                invoice_item = last_item
                self._recalculate_invoice_item(amount_to_remove, invoice_item)

        service_items = source_invoice.filter_service_items()

        for service_item in service_items.all():
            self.copy_invoice_item(correction_invoice, service_item)

        add_tag_to_invoice_item(correction_request, correction_invoice)
        set_additional_total_when_united_kingdom_lte_tax_threshold(correction_invoice)

        correction_request.correction_invoice = correction_invoice
        correction_request.correction_invoice.create_pdf()
        correction_request.save()
        return correction_request

    def calculate_percentage_discount(self, correction_amount_gross, invoice_items):
        total_gross = sum([item.gross_price for item in invoice_items.all()])
        if total_gross:
            return old_div(correction_amount_gross, Decimal(total_gross))
        return 100

    def copy_invoice_item(self, correction_invoice, invoice_item):
        old_pk = invoice_item.pk
        invoice_item.pk = None
        invoice_item.invoice_id = correction_invoice.id
        invoice_item.corrected_invoice_item_id = old_pk
        invoice_item.save()
        return invoice_item

    def _recalculate_invoice_item(self, amount_to_remove, invoice_item):
        if invoice_item.gross_price < amount_to_remove:
            raise ValueError('Not enough money to create correction')

        invoice_item.gross_price -= amount_to_remove
        if invoice_item.vat_status == VatType.NORMAL:
            vat_amount = (
                old_div(invoice_item.gross_price, (invoice_item.vat_rate + 1))
                * invoice_item.vat_rate
            ).quantize(Decimal('.01'))
        else:
            vat_amount = 0

        invoice_item.vat_amount = vat_amount
        invoice_item.net_value = invoice_item.gross_price - invoice_item.vat_amount
        if invoice_item.quantity == 0:
            net_value_per_item = invoice_item.net_value
        else:
            net_value_per_item = invoice_item.net_value / invoice_item.quantity
        discount_per_item = invoice_item.net_price - net_value_per_item
        invoice_item.discount_value = discount_per_item

        invoice_item.save()


class CorrectionRequestFreeReturnStrategy(AbstractCorrectionRequestStrategy):
    def prepare_correction_request(self) -> 'CSCorrectionRequest':
        correction_request = self.correction_request
        correction_invoice, source_invoice = self._set_up_correction()
        self._set_deleted_items_to_correction(source_invoice, correction_invoice)
        correction_request.correction_invoice = correction_invoice

        add_tag_to_invoice_item(correction_request, correction_invoice)
        set_additional_total_when_united_kingdom_lte_tax_threshold(correction_invoice)

        correction_request.correction_invoice.create_pdf()
        correction_request.save()
        return correction_request

    def _set_deleted_items_to_correction(self, original_invoice, correction_invoice):
        correction_request = self.correction_request
        deleted_invoice_items = correction_request.deleted_invoice_items.all()
        if correction_request.tag == InvoiceItemTag.FREE_RETURN:
            deleted_invoice_items = (
                correction_request.deleted_invoice_items.exclude(
                    item_type=InvoiceItemType.ASSEMBLY
                )
                .exclude(item_type=InvoiceItemType.DELIVERY)
                .exclude(item_type=InvoiceItemType.SERVICE)
            )

        deleted_invoice_items_ids = list(
            deleted_invoice_items.values_list('id', flat=True)
        )
        for invoice_item in original_invoice.invoice_items.all():
            old_pk = invoice_item.pk
            invoice_item.pk = None
            invoice_item.invoice_id = correction_invoice.id
            invoice_item.corrected_invoice_item_id = old_pk
            if old_pk in deleted_invoice_items_ids:
                invoice_item.quantity = 0
                invoice_item.gross_price = 0
                invoice_item.vat_amount = 0
                invoice_item.net_value = 0
                invoice_item.discount_value = 0
            invoice_item.save()
