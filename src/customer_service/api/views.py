import logging

from typing import (
    TYPE_CHECKING,
    Any,
)

from rest_framework import (
    generics,
    status,
)
from rest_framework.generics import get_object_or_404
from rest_framework.response import Response
from rest_framework.views import APIView

from customer_service.api.permissions import (
    IsCustomerServiceUser,
    IsSuperUser,
)
from customer_service.api.serializers import (
    ProductPostponePrioritySerializer,
    RequestProductPostponePrioritySerializer,
)
from customer_service.models import OrderItemAbortRequest
from producers.errors import CannotChangeToPostponePriorityException
from producers.models import Product

if TYPE_CHECKING:
    from rest_framework.request import Request


logger = logging.getLogger('cstm')


class ProductPostponePriorityApiView(APIView):
    permission_classes = (IsCustomerServiceUser | IsSuperUser,)

    def post(self, request, pk, *args, **kwargs):
        product = get_object_or_404(Product, pk=pk)

        serializer = RequestProductPostponePrioritySerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        requested_postponed_delivery_date = serializer.validated_data[
            'requested_postponed_delivery_date'
        ]
        try:
            product.priority_updater.change_to_postponed(
                requested_postponed_delivery_date
            )
        except CannotChangeToPostponePriorityException as e:
            return Response(
                data={'error': e.message}, status=status.HTTP_400_BAD_REQUEST
            )

        data = ProductPostponePrioritySerializer(product).data
        return Response(data=data, status=status.HTTP_200_OK)


class OrderItemAbortRequestRollbackView(generics.UpdateAPIView):
    queryset = OrderItemAbortRequest.objects.all()
    permission_classes = (IsCustomerServiceUser | IsSuperUser,)

    def update(self, request: 'Request', *args: Any, **kwargs: Any) -> 'Response':
        abort_request = self.get_object()
        abort_request.update_status_to_cancelled()
        return Response(
            {'detail': 'Status updated to CANCELLED.'}, status=status.HTTP_200_OK
        )
