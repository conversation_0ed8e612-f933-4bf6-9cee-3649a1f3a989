# Generated by Django 4.2.23 on 2025-07-08 13:43

import django.db.models.deletion

from django.conf import settings
from django.db import (
    migrations,
    models,
)


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('orders', '0066_order_delivery_offset'),
        ('customer_service', '0037_alter_mentionmeb2breward_unique_together_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='OrderItemAbortRequest',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name='ID',
                    ),
                ),
                (
                    'status',
                    models.IntegerField(
                        choices=[(0, 'Pending'), (1, 'Cancelled')], default=0
                    ),
                ),
                (
                    'order_items_with_quantity',
                    models.JSONField(blank=True, default=list),
                ),
                ('requested_at', models.DateTimeField(auto_now_add=True)),
                ('executed_at', models.DateTimeField(blank=True, null=True)),
                ('exception_messages', models.CharField(blank=True, max_length=256)),
                (
                    'order',
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name='abort_requests',
                        to='orders.order',
                    ),
                ),
                (
                    'requested_by',
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                'ordering': ['-requested_at'],
            },
        ),
    ]
