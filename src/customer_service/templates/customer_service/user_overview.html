{% extends "customer_service/base.html" %}
{% load i18n %}
{% load admin_urls %}
{% load activity_tags %}
{% load humanize %}
{% load cs_tags %}
{% load static %}

{% block extrahead %}
    <link href="{% static 'css/bootcards-desktop.css' %}" rel="stylesheet">
    <script src="{% static 'js/bootcards.min.js' %}"></script>
    <script src="{% static 'js/rollbackAbortRequest.js' %}"></script>
    <style>
        {# IN_PRODUCTION #}
        .status_color_3 {
            color: #00A000;
            font-weight: bold;
        }

        {# ORDER IS CONNECTED TO ACTIVE CART #}
        .active_cart_color {
            color: #ff6a20;
            font-weight: bold;
        }

        {# CANCELLED, PAYMENT_FAILED #}
        .status_color_0, .status_color_6 {
            color: #C20000;
            font-weight: bold;
        }

        {# PAYMENT_PENDING #}
        .status_color_2 {
            color: #BC7A00;
            font-weight: bold;
        }

        .priority_color_90 span {
            background-color: #ffff00;
            font-weight: bold;
        }

        #order_notes_plain img {
            max-width: 100%;
        }

        .email24-table {
            border: 1px solid black;
            border-collapse: collapse;
            padding: 5px;
        }

        .email24-table-header {
            border: 1px solid black;
            border-collapse: collapse;
            padding-top: 5px;
            text-align: center;
        }

        .as_accepted {
            background-color: #139720;
        }

        .as_rejected {
            background-color: #FF4500;
        }

        .as_offered {
            background-color: #98FB98;
        }

        .as_in_progress {
            background-color: #F08080;
        }

        .as_ready_to_propose {
            background-color: #98FB98;
        }

        .as_proposed_to_client {
            background-color: #F0E68C;
        }

        .as_accepted_by_client {
            background-color: #F0E68C;
        }

        .as_rejected_by_client {
            background-color: #B22222;
        }

        .postpone-form {
            display: flex;
            justify-content: space-between;
            margin-top: 20px;
            gap: 20px;
        }

        .logistic-order-active {
            padding-left: 30px;
            background: repeating-linear-gradient(
                45deg,
                transparent,
                transparent 10px,
                #ccc 10px,
                #ccc 20px
            );
        }
        .collapse-border {
            border-collapse: collapse;
        }
        .as-max-status-cell {
            padding: 5px;
            border: solid 1px;
        }
    </style>
{% endblock %}


{% block content %}
    {% include 'components/tag_user_overview.html' %}
    {% include 'components/tag_library_overview.html' %}
    {% if cart != None %}
        {% show_short_order_overview cart payment_link lat %}
    {% endif %}
    {% include 'components/tag_user_activities_overview.html' %}

    <div class="panel-group" role="tablist" aria-multiselectable="true">
        {% for order in orders %}
            {% show_long_order_overview order payment_link lat order %}
            {% for logistic_order in order.logistic_info %}
                <div class="logistic-order-active">
                    {% show_long_logistic_overview order logistic_order %}
                </div>
            {% endfor %}
        {% endfor %}
    </div>

{% endblock %}

{% block extrascripts %}
    <script type="application/javascript">
        $(document).ready(function () {
            $('.cart__item__order_item__preview').unbind('click')
            $('#orderList .cart__item__order_item__preview').click(function (e) {
                e.preventDefault();
                $(e.target).toggleClass('make_big');
                if ($(e.target).hasClass('make_big')) {
                    $(e.target).animate({
                        width: "760px",
                        height: "563px"
                    }, 500)
                } else {
                    $(e.target).animate({
                        width: "135px",
                        height: "100px"
                    }, 500)
                }
            });

            $('.item__order_item__preview').unbind('click');
            $('.item__order_item__preview').click(function (e) {
                e.preventDefault();
                $(e.target).toggleClass('make_big');
                if ($(e.target).hasClass('make_big')) {
                    $(e.target).animate({
                        width: "760px",
                        height: "563px"
                    }, 500)
                } else {
                    $(e.target).animate({
                        width: "270px",
                        height: "200px"
                    }, 500)
                }
            });

            $('#addSampleBoxToCartModal').on('show.bs.modal', function (event) {
                var button = $(event.relatedTarget);
                var order_id = button.data('order_id');
                var modal = $(this);
                modal.find('.modal-body input[name="order_id"]').val(order_id);
            });

            $('#addSampleBoxToCartModal .btn-submit').on('click', function () {
                $('#addSampleBoxToCartModal form').submit();
            });

            var csrftoken = jQuery("[name=csrfmiddlewaretoken]").val();

            function csrfSafeMethod(method) {
                // these HTTP methods do not require CSRF protection
                return (/^(GET|HEAD|OPTIONS|TRACE)$/.test(method));
            }

            $.ajaxSetup({
                beforeSend: function (xhr, settings) {
                    if (!csrfSafeMethod(settings.type) && !this.crossDomain) {
                        xhr.setRequestHeader("X-CSRFToken", csrftoken);
                    }
                }
            });
            $('.order_notes_plain, .order_notes_header').dblclick(function (event) {
                $(this).parent().find('.order_notes_plain').addClass('hidden');
                $(this).parent().find('.order_notes_editor').removeClass('hidden');
            });
            $('.order_notes_editor_submit').click(function (event) {
                oid = $(this).attr('oid');
                const plainText = $('#editor_' + oid).val();
                $('#order_notes_plain_' + oid).html(plainText);
                $('#order_notes_plain_' + oid).removeClass('hidden');
                $('#order_notes_editor_' + oid).addClass('hidden');
                $('#order_notes_header_loader_' + oid).removeClass('hidden');
                $.ajax({
                    url: $(this).parent().attr('action'),
                    method: "POST",
                    data: {
                        csrfmiddlewaretoken: csrftoken,
                        text: plainText
                    }
                }).done(function () {
                    $('#order_notes_header_loader_' + oid).addClass('hidden');
                })
            });
            const orderAbortButtons = document.querySelectorAll('.order-abort');
            orderAbortButtons.forEach(orderAbortButton => {
                orderAbortButton.addEventListener('click', (event) => {
                    const message = (
                        'Are you sure you want to abort this order ?'
                    )
                    if (!confirm(message)) {
                        event.preventDefault();
                    }
                    return;
                });
            });

            const rollbackAbortButtons = document.querySelectorAll('.rollback-order-abort');
            rollbackAbortButtons.forEach(rollbackAbortButton => {
                rollbackAbortButton.addEventListener('click', (event) => {
                    const message = (
                        'Are you sure you want to rollback abort this order ?'
                    )
                    if (!confirm(message)) {
                        event.preventDefault();
                    }
                    return;
                });
            });

            const postponeForms = document.querySelectorAll(".postpone-form");
            postponeForms.forEach(postponeForm => {
                postponeForm.addEventListener("submit", (event) => {
                    event.preventDefault();
                    const form = event.currentTarget;
                    const requestedPostponedDeliveryDate = event.currentTarget.id_requested_postponed_delivery_date.value;

                    fetch(form.action, {
                        method: 'POST',
                        body: JSON.stringify({
                            requested_postponed_delivery_date: requestedPostponedDeliveryDate
                        }),
                        headers: {
                            'Content-Type': 'application/json'
                        }
                    })
                        .then(response => {
                            if (!response.ok) {
                                return response.json().then(errorResponse => {
                                    throw new Error(errorResponse.error);
                                });
                            }
                            return response.json();
                    })
                        .then(response => {
                            const productPostponeDate = document.getElementById(`postpone-date-${response.id}`);
                            productPostponeDate.innerHTML = `
                        <p>Requested Postponed Delivery Date: ${response.requested_postponed_delivery_date}</p>
                        <p>Expected Postponed Release Date: ${response.expected_postponed_release_date}</p>`;
                        })
                        .catch(error => {
                            window.alert(error);
                        });
                    });
                });
            });

    </script>
    <script src="{% static 'js/preventDoubleClick.js' %}"></script>
{% endblock %}
