from typing import Optional

from django.contrib.auth.models import User
from django.db import transaction
from django.db.models import QuerySet
from django.utils import translation
from django.utils.translation import get_language_from_request

from rest_framework import status
from rest_framework.authentication import TokenAuthentication
from rest_framework.decorators import action
from rest_framework.generics import (
    GenericAPIView,
    UpdateAPIView,
)
from rest_framework.mixins import RetrieveModelMixin
from rest_framework.permissions import IsAuthenticated
from rest_framework.request import Request
from rest_framework.response import Response
from rest_framework.viewsets import (
    GenericViewSet,
    ModelViewSet,
)

from carts.choices import CartStatusChoices
from carts.exceptions import (
    FurnitureNotFoundError,
    MaxCartSizeError,
    MinCartItemSizeError,
)
from carts.models import (
    Cart,
    CartItem,
)
from carts.serializers import (
    AssemblySerializer,
    CartItemAsssemblySerializer,
    CartItemIdentifierSerializer,
    CartSerializer,
    CreateCartItemSerializer,
    DeliveryServicesSerializer,
    EmptyCartSerializer,
    MailingCartSerializer,
    OldSofaCollectionSerializer,
)
from carts.services.cart_service import CartService
from custom.enums import (
    Furniture,
    LanguageEnum,
)
from custom.metrics import metrics_client
from custom.permissions import BrazePermission
from custom.shelf_states_interactor import add_jetty_state_to_redis
from custom.utils.url import get_request_source
from events.domain_events.marketing_events import (
    CartEmptyEvent,
    CartUpdateEvent,
)
from gallery.enums import (
    FurnitureStatusEnum,
    SellableItemContentTypes,
    ShelfStatusSource,
)
from gallery.services.copy_furniture import copy_furniture
from gallery.services.prices_for_serializers import get_currency_rate
from gallery.types import (
    FurnitureType,
    SellableItemType,
)
from promotions.utils import (
    get_active_promotion,
    get_strikethrough_promo_data,
)
from regions.cached_region import get_region_data_from_request
from regions.mixins import RegionCalculationsObject
from services.errors import (
    OldSofaCollectionValidationError,
    WhiteGlovesDeliveryValidationError,
)
from user_profile.decorators import create_and_login_user


class CartViewSet(ModelViewSet):
    permission_classes = (IsAuthenticated,)
    serializer_class = CartSerializer

    def get_queryset(self) -> QuerySet[Cart]:
        return (
            Cart.objects.select_related(
                'owner__profile',
                'region',
                'region__currency',
                'used_promo',
            )
            .prefetch_related(
                'items',
                'items__content_type',
                'items__cart_item',
            )
            .filter(owner=self.request.user)
        )

    def get_serializer_context(self, *args, **kwargs):
        cart = self.get_object()
        cached_region_data = cart.region.cached_region_data
        strikethrough_promo_data = get_strikethrough_promo_data(cart)
        strikethrough_voucher = strikethrough_promo_data.strikethrough_voucher
        has_strikethrough_promo_applied = (
            strikethrough_promo_data.has_strikethrough_promo_applied
        )
        active_promotion = get_active_promotion(cached_region_data)
        active_promotion_config = (
            active_promotion.configs.last() if active_promotion else None
        )

        return {
            **super().get_serializer_context(),
            'language': get_language_from_request(self.request),
            'region': cached_region_data,
            'strikethrough_voucher': strikethrough_voucher,
            'has_strikethrough_promo_applied': has_strikethrough_promo_applied,
            'active_promotion': active_promotion,
            'active_promotion_config': active_promotion_config,
            'region_calculations_object': (
                RegionCalculationsObject(region=cached_region_data)
            ),
            'currency_rate': get_currency_rate(cached_region_data),
        }

    @action(detail=False, methods=['get'], permission_classes=[])
    def empty_response(self, request):
        cached_region_data = get_region_data_from_request(self.request)
        active_promotion = get_active_promotion(cached_region_data)
        active_promotion_config = (
            active_promotion.configs.last() if active_promotion else None
        )
        context = {
            **super().get_serializer_context(),
            'language': get_language_from_request(self.request),
            'region': cached_region_data,
            'currency_code': cached_region_data.currency_code,
            'active_promotion': active_promotion,
            'active_promotion_config': active_promotion_config,
        }
        serializer = EmptyCartSerializer({}, context=context)
        return Response(serializer.data)

    @create_and_login_user(register_referer='webgl addtocart')
    @action(detail=False, methods=['post'], permission_classes=[])
    def create_cart(self, request):
        cart = CartService.create_cart(request.user)
        return Response({'id': cart.id}, status=status.HTTP_201_CREATED)

    @action(detail=True, methods=['post'])
    def add_to_cart(self, request, pk=None):
        """
        Add to Cart by giving the id and type of furniture
        """
        cart = self.get_object()
        serializer = CreateCartItemSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        try:
            new_sellable_item = self._copy_furniture(
                data=serializer.data, owner=cart.owner, request=request
            )
            service = CartService(cart)
            service.add_to_cart(new_sellable_item, quantity=serializer.data['quantity'])
        except FurnitureNotFoundError as e:
            return Response(e.message, status=status.HTTP_400_BAD_REQUEST)
        except MaxCartSizeError as e:
            return Response(e.message, status=status.HTTP_400_BAD_REQUEST)

        self._emit_cart_update_events(
            cart, content_type=new_sellable_item.furniture_type
        )
        self._update_analytics(new_sellable_item, cart.owner)
        return Response(status=status.HTTP_201_CREATED)

    @action(detail=True, methods=['post'])
    def delete_from_cart(self, request, pk=None):
        if request.data.get('content_type') == 'sample_box':
            # cast
            request.data['content_type'] = SellableItemContentTypes.SAMPLE_BOX

        serializer = CartItemIdentifierSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        cart = self.get_object()
        service = CartService(cart)

        try:
            service.delete_from_cart(**serializer.data)
        except CartItem.DoesNotExist as exception:
            return Response(
                {'detail': str(exception)},
                status=status.HTTP_400_BAD_REQUEST,
            )
        else:
            self._emit_cart_update_events(
                cart,
                content_type=serializer.data['content_type'],
            )

        return Response(status=status.HTTP_204_NO_CONTENT)

    @action(detail=True, methods=['post'])
    def change_assembly(self, request, pk=None):
        cart = self.get_object()
        serializer = AssemblySerializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        service = CartService(cart)
        try:
            service.change_assembly(activate=serializer.data['assembly'])
        except ValueError as exception:
            return Response(
                {'detail': str(exception)},
                status=status.HTTP_400_BAD_REQUEST,
            )

        return Response(status=status.HTTP_200_OK)

    @action(detail=True, methods=['post'])
    def change_old_sofa_collection(self, request, pk=None):
        cart = self.get_object()
        serializer = OldSofaCollectionSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        service = CartService(cart)
        try:
            service.change_old_sofa_collection(
                activate=serializer.data['old_sofa_collection']
            )
        except (ValueError, OldSofaCollectionValidationError) as exception:
            return Response(
                {'detail': str(exception)},
                status=status.HTTP_400_BAD_REQUEST,
            )

        return Response(status=status.HTTP_200_OK)

    @action(detail=True, methods=['post'])
    def change_delivery_services(self, request, pk=None):
        """
        Sotties have white gloves delivery
        other furniture have assembly
        """
        cart = self.get_object()
        serializer = DeliveryServicesSerializer(
            data=request.data, context={'cart': cart}
        )
        serializer.is_valid(raise_exception=True)

        service = CartService(cart)
        try:
            with transaction.atomic():
                if 'assembly' in serializer.data:
                    service.change_assembly(activate=serializer.data['assembly'])
                if 'white_gloves_delivery' in serializer.data:
                    service.change_white_gloves_delivery(
                        activate=serializer.data['white_gloves_delivery']
                    )
        except (ValueError, WhiteGlovesDeliveryValidationError) as exception:
            return Response(
                {'detail': str(exception)},
                status=status.HTTP_400_BAD_REQUEST,
            )

        return Response(status=status.HTTP_200_OK)

    @action(detail=True, methods=['post'])
    def sync_with_order(self, request, pk=None):
        cart = self.get_object()
        source = get_request_source(request)

        service = CartService(cart)
        order = service.sync_with_order(request=request, source=source)

        return Response({'order_id': order.id}, status=status.HTTP_200_OK)

    def _copy_furniture(
        self, data: dict, owner: User, request: Request
    ) -> FurnitureType:
        original_item_id = data['sellable_item_id']
        model_class = Furniture(data['content_type']).model
        try:
            sellable_item = model_class.objects.get(id=original_item_id)
        except model_class.DoesNotExist:
            raise FurnitureNotFoundError(
                content_type=data['content_type'], original_item_id=original_item_id
            )

        new_furniture = copy_furniture(
            old_furniture=sellable_item,
            owner=owner,
            new_furniture_status=FurnitureStatusEnum.DRAFT,
            request=request,
        )
        return new_furniture

    @staticmethod
    def _emit_cart_update_events(
        cart: Cart,
        content_type: SellableItemContentTypes,
    ) -> None:
        cart.refresh_from_db()
        if cart.items.count() > 0:
            CartUpdateEvent(
                user=cart.owner,
                cart_id=cart.id,
                total_price=int(cart.total_price),
                is_sample_box=content_type == SellableItemContentTypes.SAMPLE_BOX,
            )
        else:
            CartEmptyEvent(user=cart.owner)

    def _update_analytics(self, sellable_item: SellableItemType, user):
        tags = [
            f'shelf_type:{sellable_item.furniture_type}',
            f'user_lang:{user.profile.language}',
        ]
        metrics_client().increment('api.save_to_cart', 1, tags=tags)

        if sellable_item.furniture_type == Furniture.jetty.value:
            add_jetty_state_to_redis(
                user_id=user.id,
                jetty=sellable_item,
                source=ShelfStatusSource.ADD_TO_CART,
                pagepath=self.request.get_full_path(),
            )


class CartItemQuantityChangeAPIView(GenericAPIView):
    lookup_url_kwarg = 'item_id'

    def get_queryset(self):
        return CartItem.objects.get_active_for_user(user=self.request.user)

    def post(self, request, *args, **kwargs):
        item = self.get_object()
        cart_service = CartService(cart=item.cart)

        action_map = {
            'increase': cart_service.increase_item_quantity,
            'decrease': cart_service.decrease_item_quantity,
        }

        action = kwargs.get('action')
        if action not in action_map:
            return Response(
                {'detail': 'Invalid action'}, status=status.HTTP_400_BAD_REQUEST
            )

        try:
            action_map[action](item)
        except (MaxCartSizeError, MinCartItemSizeError) as e:
            return Response({'detail': e.message}, status=status.HTTP_400_BAD_REQUEST)

        return Response({'id': item.id, 'new_quantity': item.quantity})


class CartItemEnableAssemblyAPIView(UpdateAPIView):
    lookup_url_kwarg = 'item_id'
    serializer_class = CartItemAsssemblySerializer

    def get_queryset(self):
        return CartItem.objects.get_active_for_user(user=self.request.user)

    def update(self, request, *args, **kwargs):
        cart_item = self.get_object()
        serializer = self.get_serializer(cart_item, data=request.data, partial=True)
        serializer.is_valid(raise_exception=True)
        self.perform_update(serializer)

        CartService.recalculate_cart(cart=cart_item.cart)

        return Response(serializer.data)


class MailingCartStatusAPIView(RetrieveModelMixin, GenericViewSet):
    authentication_classes = (TokenAuthentication,)
    permission_classes = (BrazePermission,)
    queryset = Cart.objects.all()
    serializer_class = MailingCartSerializer

    def retrieve(self, request, *args, **kwargs):
        instance = self.get_object()
        return self._serialize_response(instance)

    @action(
        detail=False,
        methods=['get'],
        url_path=r'user/(?P<user_id>\d+)',
        url_name='by-user',
    )
    def retrieve_by_user(self, request, user_id=None):
        instance = self.get_cart_by_user(user_id)
        if not instance:
            return Response(
                {'detail': 'Cart not found for this user'},
                status=status.HTTP_404_NOT_FOUND,
            )
        return self._serialize_response(instance)

    def get_cart_by_user(self, user_id) -> Optional[Cart]:
        return (
            Cart.objects.filter(
                owner__id=user_id,
                status__in={
                    CartStatusChoices.ACTIVE,
                    CartStatusChoices.DRAFT,
                },
            )
            .order_by('-created_at')
            .first()
        )

    def _serialize_response(self, instance):
        language = LanguageEnum(instance.owner.profile.language)
        translation.activate(language)
        serializer = self.get_serializer(
            instance,
            context=self.get_serializer_context() | {'region': instance.region},
        )
        return Response(serializer.data)
