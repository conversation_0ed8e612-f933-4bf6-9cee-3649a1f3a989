from rest_framework import status
from rest_framework.exceptions import APIException

from carts.constants import MAX_CART_SIZE


class FurnitureNotFoundError(Exception):
    MSG = '{content_type}: {original_item_id} not found'

    def __init__(self, content_type: str, original_item_id: str):
        message = self.MSG.format(
            content_type=content_type.title(), original_item_id=original_item_id
        )
        super().__init__(message)


class MaxCartSizeError(Exception):
    message = (
        'The maximum cart size has been exceeded. '
        'The total number of products in the cart '
        f'must be less than or equal to {MAX_CART_SIZE}.'
    )


class MinCartItemSizeError(Exception):
    message = 'Quantity cannot be decreased below 1'


class InactiveCartException(APIException):
    status_code = status.HTTP_400_BAD_REQUEST
    default_detail = 'Cart is not active'
    default_code = 'inactive_cart'
