import logging
import random
import string

from decimal import (
    ROUND_HALF_UP,
    Decimal,
)
from typing import (
    TYPE_CHECKING,
    Optional,
    Union,
)

from django.conf import settings
from django.contrib.auth.models import User
from django.core.cache import cache
from django.core.exceptions import MultipleObjectsReturned
from django.db import models
from django.db.models import (
    Q,
    QuerySet,
)
from django.utils import timezone
from django.utils.functional import cached_property

from custom.enums import (
    Furniture,
    SampleBoxVariantEnum,
    ShelfType,
)
from custom.metrics import metrics_client
from custom.models import SingletonManager
from custom.models.validators import DateRangeValidator
from custom.utils.delivery import TimeHelper
from custom.utils.in_memory_cache import expiring_lru_cache
from gallery.constants import FURNITURE_DYNAMIC_COLOR_CHOICES
from gallery.enums import FurnitureCategory
from gallery.models import SellableItemAbstract
from regions.constants import REGION_CACHE_PERIOD
from regions.mixins import (
    RegionalizedMixin,
    RegionCalculationsObject,
)
from regions.models import Region
from regions.types import RegionLikeObject
from vouchers.constants import (
    VOUCHER_B2B_PREFIX,
    VOUCHER_KSB2B_PREFIX,
)
from vouchers.enums import (
    ServiceType,
    VoucherOrigin,
    VoucherType,
)
from vouchers.exceptions import VoucherCodeUniquenessError

if TYPE_CHECKING:
    from carts.models import Cart
    from gallery.models import (
        Jetty,
        Watty,
    )
    from orders.models import Order

logger = logging.getLogger('cstm')


def normalize_fraction(d):
    if isinstance(d, Decimal):
        return d.to_integral() if d == d.to_integral() else d
    else:
        return d


class VoucherSettings(models.Model):
    # field hidden, class method `b2b_voucher_percentage_value should` be used instead
    _b2b_voucher_percentage_value = models.DecimalField(max_digits=2, decimal_places=0)

    _b2b_sotty_discount_value = models.DecimalField(
        default=10.0,
        max_digits=2,
        decimal_places=0,
        null=True,
        blank=True,
    )
    _b2b_delivery_discount_value = models.DecimalField(
        max_digits=2,
        decimal_places=0,
        null=True,
        blank=True,
    )

    objects = SingletonManager()

    class Meta:
        verbose_name = 'Voucher Settings'
        verbose_name_plural = 'Voucher Settings'

    def save(self, *args, **kwargs):
        from vouchers.utils import update_b2b_percentage_vouchers

        created = self.id is None
        super().save(*args, **kwargs)
        if created:
            return

        update_b2b_percentage_vouchers(self._b2b_voucher_percentage_value)
        self.b2b_voucher_percentage_value.cache_clear()

    @classmethod
    @expiring_lru_cache(ttl=60 * 60)
    def b2b_voucher_percentage_value(cls):
        return cls.objects.first()._b2b_voucher_percentage_value

    @classmethod
    @expiring_lru_cache(ttl=60 * 60)
    def get_b2b_sotty_discount_value(cls):
        return cls.objects.first()._b2b_sotty_discount_value

    @classmethod
    @expiring_lru_cache(ttl=60 * 60)
    def get_b2b_delivery_discount_value(cls):
        return cls.objects.first()._b2b_delivery_discount_value


class VoucherEventType(models.Model):
    name = models.CharField(max_length=30, primary_key=True)
    description = models.CharField(max_length=255, null=True)


class VoucherEvent(models.Model):
    voucher = models.ForeignKey('Voucher', on_delete=models.CASCADE)
    user = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
    )
    event_type = models.ForeignKey(VoucherEventType, on_delete=models.CASCADE)


class VoucherGroup(models.Model):
    code = models.CharField(max_length=255, unique=False)
    region = models.ManyToManyField(Region)

    def __str__(self):
        return 'Group: {} - id: {} '.format(self.code, self.id)


class VoucherManager(models.Manager):
    def active_b2b_vouchers(self) -> QuerySet['Voucher']:
        return (
            self.get_queryset()
            .filter(
                active=True,
                origin=VoucherOrigin.B2B,
                kind_of=VoucherType.PERCENTAGE,
                quantity_left__gt=0,
            )
            .filter(
                Q(code__istartswith=VOUCHER_KSB2B_PREFIX)
                | Q(code__istartswith=VOUCHER_B2B_PREFIX),
            )
        )


class Voucher(models.Model):
    BUSINESS_VOUCHER_PREFIXES = ['dtp', 'b2b']
    INFLUENCER_GENERIC_CODE = 'influencersxtylko'

    kind_of = models.PositiveSmallIntegerField(
        choices=VoucherType.choices,
        default=VoucherType.ABSOLUTE,
    )
    code = models.CharField(max_length=255, unique=True)
    creator = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
    )
    origin = models.IntegerField(choices=VoucherOrigin.choices)
    group = models.ForeignKey(
        VoucherGroup,
        blank=True,
        null=True,
        on_delete=models.CASCADE,
    )

    start_date = models.DateTimeField(blank=True, null=True)
    end_date = models.DateTimeField(blank=True, null=True)

    value = models.FloatField(
        'In euros or in XX%', default=0.0
    )  # percentage or absolute
    active = models.BooleanField(default=True)  # override start/end dates

    quantity = models.IntegerField(default=0, help_text='-1 for unlimited')
    quantity_left = models.PositiveSmallIntegerField(default=0)
    amount_starts = models.FloatField(default=0.0)
    amount_limit = models.FloatField(default=0.0)  # in euros, needs to be set
    related_users = models.ManyToManyField(
        User, related_name='related_vouchers', through=VoucherEvent
    )
    created_at = models.DateTimeField(auto_now_add=True)
    item_conditionals = models.JSONField(null=True, blank=True)

    discounts = models.ManyToManyField(
        'vouchers.ItemDiscount',
        blank=True,
        help_text='Do not add discounts for absolute vouchers.',
    )
    notes = models.TextField(blank=True)
    ignore_on_invoice = models.BooleanField(blank=True, null=True)
    for_email = models.EmailField(blank=True)

    objects = VoucherManager()

    class Meta(object):
        verbose_name = 'Voucher detail'
        verbose_name_plural = 'Vouchers'
        ordering = [
            '-created_at',
        ]

    def is_active(self):
        if self.active is False:
            return False
        if self.quantity >= 0 and self.quantity_left <= 0:
            return False
        if self.start_date is not None and self.start_date > timezone.now():
            return False
        if self.end_date is not None and self.end_date <= timezone.now():
            return False
        return True

    def is_absolute(self):
        return self.kind_of == VoucherType.ABSOLUTE

    def is_percentage(self):
        return self.kind_of == VoucherType.PERCENTAGE

    def is_geometry_affected(self, geometry):
        item_filters = self.item_filters
        if not item_filters:
            return True
        exclude_filters = item_filters.pop('exclude', [])
        include_filters = item_filters.pop('include', [])
        return self.check_conditions(geometry, include_filters, exclude_filters)

    # TODO: should items be sorted by .aggregate_region_price?
    def get_promotion_affected_items(
        self,
        instance: Union['Order', 'Cart'],
    ) -> Union[list, QuerySet]:
        qs = instance.material_items.all()
        if not self.item_filters:
            return sorted(qs, key=lambda x: x.region_price, reverse=True)
        if instance.is_order and instance.check_if_voucher_applies_for_switch(self):
            return sorted(qs, key=lambda x: x.region_price, reverse=True)
        exclude_filters = self.item_filters.pop('exclude', [])
        include_filters = self.item_filters.pop('include', [])

        return sorted(
            [
                item
                for item in qs
                if self.check_conditions(
                    item.sellable_item,
                    include_filters,
                    exclude_filters,
                )
            ],
            key=lambda item: item.region_price,
            reverse=True,
        )

    def check_conditions(self, item, filters, exclude_filters):
        for include_filter in filters:
            if not self._check_filters(item, include_filter):
                return False
        for exclude_filter in exclude_filters:
            if self._check_filters(item, exclude_filter):
                return False
        return True

    def _check_filters(self, item, conditions):
        for filter in conditions:
            in_condition = False
            filter_name = filter
            if filter.endswith('__in'):
                filter_name = filter[:-4]
                in_condition = True
            try:
                value = getattr(item, filter_name)
            except AttributeError:
                # item might be of different types (Jetty, SampleBox etc.)
                # we return False for cases when we can't get attribute
                return False
            if in_condition and value not in conditions[filter]:
                return False
            elif not in_condition and not value == conditions[filter]:
                return False

        return True

    def calculate_amount_after_voucher(
        self,
        instance: Union['Order', 'Cart'],
        region: Optional[RegionLikeObject] = None,
    ) -> Decimal:
        if self.kind_of == VoucherType.ABSOLUTE:
            region_total_price = instance.get_total_value()
            return self.calculate_price(region_total_price, region)
        elif self.kind_of == VoucherType.PERCENTAGE:
            promotion_affected_items = self.get_promotion_affected_items(instance)
            return self.calculate_price_with_discounts(
                instance, promotion_affected_items
            )

    def is_influencers_origin(self) -> bool:
        return self.origin == VoucherOrigin.INFLUENCERS_BARTER_DEAL

    def is_legit_barter_deal(self, region: RegionLikeObject) -> bool:
        return (
            self.origin == VoucherOrigin.INFLUENCERS_BARTER_DEAL
            and hasattr(self, 'barter_deal')
            and self.barter_deal.currency == region.currency
        )

    def subtract_quantity_left(self):
        if self.quantity_left <= 0:
            return

        self.quantity_left -= 1
        self.save(update_fields=['quantity_left'])
        metrics_client().increment(
            'backend.voucher.use',
            1,
            tags=[f'origin:{self.origin}'],
        )

    def check_discounted_amount(
        self,
        total_price: Decimal,
        geometry: Union['Jetty', 'Watty'],
        region: Optional[RegionLikeObject] = None,
    ) -> Decimal:
        """Checks if the voucher applies and if so returns discounted price."""
        if not self.is_geometry_affected(geometry):
            return total_price

        voucher_conditions_passed = self._check_voucher_conditions(total_price, region)
        if not voucher_conditions_passed:
            return total_price
        price = self.calculate_price_for_furniture(geometry, total_price)
        if self.kind_of == VoucherType.ABSOLUTE:
            price = self.calculate_price(total_price, region)
        return price.quantize(Decimal('1'), rounding=ROUND_HALF_UP)

    def calculate_price_with_discounts(
        self,
        instance: Union['Order', 'Cart'],
        promotion_affected_items: Union[list, QuerySet],
    ) -> Decimal:
        affected_ids = [item.id for item in promotion_affected_items]
        items_price = 0
        # Use material_items to match get_promotion_affected_items behavior
        # This ensures consistency - additional services are not considered for voucher
        # calculations (yet)
        for item in instance.material_items.all():
            if not item.sellable_item:
                continue
            if instance.assembly:
                items_price += item.aggregate_region_assembly_price
            if item.id not in affected_ids:
                items_price += item.aggregate_region_price
                continue

            items_price += self.get_delivery_price_with_discount(
                item.aggregate_region_delivery_price
            )
            items_price += self.calculate_price_for_furniture(
                item.sellable_item, item.aggregate_region_price
            )
        return Decimal(items_price).quantize(Decimal('1'))

    @cached_property
    def delivery_discounts(self) -> QuerySet['ItemDiscount']:
        return self.discounts.filter(service_type=ServiceType.DELIVERY)

    @cached_property
    def furniture_discounts(self) -> QuerySet['ItemDiscount']:
        return self.discounts.filter(service_type__isnull=True)

    @property
    def excludes_sofas(self):
        return self.discounts.filter(
            Q(
                shelf_type=ShelfType.SOFA_TYPE01,
                value=0,
            )
            | Q(
                furniture_type=Furniture.sotty.value,
                value=0,
            )
        ).exists()

    def get_delivery_price_with_discount(
        self,
        delivery_price: Decimal,
    ) -> Decimal:
        if self.kind_of == VoucherType.ABSOLUTE or delivery_price == 0:
            return delivery_price
        for delivery_discount in self.delivery_discounts:
            if delivery_discount.check_condition_for_delivery():
                return delivery_discount.calculate_price(delivery_price)
        return delivery_price

    def _check_voucher_conditions(
        self,
        price,
        region: Optional[RegionLikeObject] = None,
    ):
        region_entry = self.get_region_entry(region)
        return region_entry.amount_starts <= price <= region_entry.amount_limit

    def calculate_price_for_furniture(self, furniture, price):
        if not self.is_geometry_affected(furniture):
            return price

        for discount in self.furniture_discounts:
            if discount.check_conditions(furniture):
                return discount.calculate_price(price)
        return self.calculate_price(price)

    def get_discount_value_for_item(self, item):
        """for global vouchers or when region does not matter"""
        for discount in self.discounts.all():
            if discount.check_conditions(item):
                return discount.value
        return self.value

    def update_voucher_entries(self, region_entries):
        for entry in self.region_entries.all():
            current_region_entry = [
                region
                for region in region_entries
                if region.get('region', None) == entry.region
            ]
            if current_region_entry:
                current_region_entry = current_region_entry[0]
                entry.amount_limit = current_region_entry.get('amount_limit', None)
                entry.amount_starts = current_region_entry.get('amount_starts', None)
                entry.value = current_region_entry.get('value', None)
                entry.save(update_fields=['amount_limit', 'amount_starts', 'value'])

    @staticmethod
    def generate_code(
        inside_string=None,
        inside_string_at_beginning=False,
        character_count=10,
        level=1,
        letters=True,
        digits=True,
        characters=None,
        tries=0,
        codes=(),
    ):
        if tries > 2:
            raise VoucherCodeUniquenessError(
                'Not able to generate unique voucher code, '
                'try increasing character count'
            )
        chars = string.digits if digits else ''
        if letters:
            chars += string.ascii_lowercase if level == 1 else string.ascii_uppercase
        if not digits and not letters and characters is not None:
            chars = characters
        _rng = random.SystemRandom()
        if inside_string is None:
            voucher_code = ''.join(_rng.choice(chars) for _ in range(character_count))
        else:
            number_of_random_chars = max(character_count - len(inside_string), 0)
            if number_of_random_chars > 0:
                string_start = (
                    random.randint(0, number_of_random_chars - 1)
                    if not inside_string_at_beginning
                    else 0
                )
                code = ''.join(
                    _rng.choice(chars) for _ in range(number_of_random_chars)
                )
                voucher_code = '{}{}{}'.format(
                    code[:string_start], inside_string, code[string_start:]
                )
            else:
                voucher_code = inside_string
        if not (
            Voucher.objects.filter(code=voucher_code).exists() or voucher_code in codes
        ):
            return voucher_code
        else:
            return Voucher.generate_code(
                inside_string=inside_string,
                inside_string_at_beginning=inside_string_at_beginning,
                character_count=character_count,
                level=level,
                letters=letters,
                digits=digits,
                characters=characters,
                tries=tries + 1,
                codes=codes,
            )

    def get_times_paid(self):
        from orders.models import PaidOrders

        return PaidOrders.objects.filter(used_promo=self).count()

    get_times_paid.short_description = 'Used #'

    def create_region_entries(self, force_create=False):
        if self.kind_of == VoucherType.ABSOLUTE or force_create:
            self.region_entries.all().delete()
            region_entries = []
            for region in Region.objects.all():
                rco = RegionCalculationsObject(region=region)
                region_entry = VoucherRegionEntry(region=region, voucher=self)
                region_entry.amount_limit = rco.calculate_regionalized(
                    self.amount_limit
                )
                region_entry.amount_starts = rco.calculate_regionalized(
                    self.amount_starts
                )
                if self.kind_of == VoucherType.ABSOLUTE:
                    region_entry.value = rco.calculate_regionalized(self.value)
                else:
                    # for percentage vouchers - lets use original value
                    region_entry.value = self.value
                region_entries.append(region_entry)
            VoucherRegionEntry.objects.bulk_create(region_entries)

    def get_region_entry(
        self,
        region: Optional[RegionLikeObject] = None,
    ) -> 'VoucherRegionEntry':
        if not region:
            return VoucherRegionEntry.return_calculated_instance(
                self, Region.get_other()
            )
        region_entry_cache_key = 'voucher_region_entry_{}_{}'.format(self.id, region.id)
        region_entry = cache.get(region_entry_cache_key)
        if region_entry is None:
            try:
                region_entry = [
                    entry
                    for entry in self.region_entries.all()
                    if entry.region_id == region.id
                ][0]
                cache.set(region_entry_cache_key, region_entry, REGION_CACHE_PERIOD)
            except IndexError:
                region_entry = VoucherRegionEntry.return_calculated_instance(
                    self, region
                )
        return region_entry

    def get_regionalized_value(self, region):
        region_entry = self.get_region_entry(region)
        return region_entry.amount

    def get_regionalized_value_display(self, region):
        if self.kind_of == VoucherType.ABSOLUTE:
            region_entry = self.get_region_entry(region)
            return region_entry.get_regionalized_value_display()
        else:
            return '{}%'.format(normalize_fraction(Decimal(self.value)))

    def clean(self):
        super().clean()
        DateRangeValidator()(self)

    def save(self, *args, **kwargs):
        self.full_clean()
        super().save(*args, **kwargs)
        if self.pk:
            region_ids = Region.objects.values_list('id', flat=True)
            for region_id in region_ids:
                # instead of removing using `delete_pattern` we iterate
                #  all of the region ids.
                #  Not the flashiest code, but quick.
                cache.delete(f'voucher_region_entry_{self.id}_{region_id}')

    @property
    def time_left_active(self):
        if self.end_date:
            days = (self.end_date - timezone.now()).days + 1
        else:
            days = 0
        return TimeHelper(days)

    def register_user_relation(self, user, relation_name):
        event_type, created = VoucherEventType.objects.get_or_create(name=relation_name)
        return VoucherEvent.objects.create(
            voucher=self, user=user, event_type=event_type
        )

    def get_regionalized_details(self, region, currency_values_dict=None):
        if not region:
            region = Region.get_other()

        if self.kind_of == VoucherType.PERCENTAGE:
            return {
                'code': self.code,
                'value_str': '{:g}%'.format(self.value),
                'amount_starts': None,
            }

        if currency_values_dict:
            value, amount_start = currency_values_dict.get(region.currency.name)
        else:
            region_entry = self.region_entries.get(region=region)
            value, amount_start = region_entry.value, region_entry.amount_starts
        return {
            'code': self.code,
            'value_str': '{}{}'.format(
                normalize_fraction(value), region.currency.symbol
            ),
            'amount_starts': '{}{}'.format(
                normalize_fraction(amount_start), region.currency.symbol
            ),
        }

    @property
    def item_filters(self):
        from vouchers.serializers import VoucherItemConditionalsSerializer

        if not self.item_conditionals:
            return None

        filters = VoucherItemConditionalsSerializer(data=self.item_conditionals)
        filters.is_valid(raise_exception=True)
        return filters.validated_data

    def calculate_price(self, price, region=None):
        if self.kind_of == VoucherType.ABSOLUTE:
            region_entry = self.get_region_entry(region)
            value = Decimal(region_entry.value)
            return max(price - value, 0)
        return max(price - Decimal(self.value / 100) * price, 0)

    def __str__(self):
        if self.is_absolute():
            return (
                'Voucher {} ({:d} left) - {} for {:f} '
                'euros (max value {:f})'.format(
                    self.code,
                    self.quantity_left,
                    self.get_kind_of_display(),
                    self.value,
                    self.amount_limit,
                )
            )
        else:
            return (
                'Voucher {} ({:d} left) - {} for {:f} '
                'percentage of order (max value {:f})'.format(
                    self.code,
                    self.quantity_left,
                    self.get_kind_of_display(),
                    self.value,
                    self.amount_limit,
                )
            )


class VoucherRegionEntry(RegionalizedMixin, models.Model):
    amount_limit = models.DecimalField(
        default=Decimal(1000000), max_digits=10, decimal_places=0
    )
    amount_starts = models.DecimalField(default=0, max_digits=8, decimal_places=0)
    region = models.ForeignKey('regions.Region', on_delete=models.CASCADE)
    value = models.DecimalField(
        'In currency (for percentage vouchers this value is ignored)',
        max_digits=8,
        decimal_places=2,
    )
    voucher = models.ForeignKey(
        Voucher,
        related_name='region_entries',
        on_delete=models.CASCADE,
    )

    class Meta(object):
        unique_together = (
            'region',
            'voucher',
        )

    def get_currency_code(self):
        return self.region.get_currency().code

    get_currency_code.short_description = 'Currency'

    def get_regionalized_value_display(self):
        return self.display_regionalized(self.value)

    @staticmethod
    def return_calculated_instance(
        voucher,
        region: Optional[RegionLikeObject] = None,
    ) -> 'VoucherRegionEntry':
        if region is None:
            region = Region.get_other()
        region_entry = VoucherRegionEntry(
            region_id=region.id,
            value=voucher.value,
            voucher=voucher,
        )
        rco = RegionCalculationsObject(region=region)
        region_entry.amount_limit = rco.calculate_regionalized(voucher.amount_limit)
        region_entry.amount_starts = rco.calculate_regionalized(voucher.amount_starts)
        if voucher.kind_of == VoucherType.ABSOLUTE:
            region_entry.value = rco.calculate_regionalized(voucher.value)
        return region_entry


class ItemDiscountManager(models.Manager):
    def resolve_or_create(self, **kwargs) -> ('ItemDiscount', bool):
        try:
            obj, created = self.get_or_create(**kwargs)
        except MultipleObjectsReturned:
            obj = self.filter(**kwargs).first()
            created = False
        return obj, created


class ItemDiscount(models.Model):
    """Used for exclusions and differentiating discounts for furniture types and colors.

    Works only with percentage vouchers, not with absolute vouchers.
    """

    FILTER_FIELDS = {
        'shelf_type',
        'furniture_type',
        'material',
        'price_order',
        'box_variant',
        'furniture_category',
    }

    value = models.FloatField(
        verbose_name='value',
        help_text='In XX%',
        default=0.0,
    )
    kind_of = models.PositiveSmallIntegerField(  # TODO: make sure this can be deleted
        choices=((VoucherType.PERCENTAGE, 'percentage'),),
        default=VoucherType.PERCENTAGE,
    )
    service_type = models.CharField(
        max_length=255,
        choices=ServiceType.choices,
        blank=True,
        null=True,
    )
    # filter fields
    shelf_type = models.PositiveSmallIntegerField(
        choices=ShelfType.choices(),
        null=True,
        blank=True,
    )
    furniture_category = models.CharField(
        choices=FurnitureCategory.choices,
        max_length=31,
        blank=True,
        default='',
    )
    furniture_type = models.CharField(
        choices=Furniture.choices(),
        max_length=10,
        blank=True,
        default='',
    )
    material = models.PositiveSmallIntegerField(
        choices=FURNITURE_DYNAMIC_COLOR_CHOICES,
        blank=True,
        null=True,
    )
    price_order = models.IntegerField(
        blank=True,
        null=True,
    )
    box_variant = models.PositiveSmallIntegerField(
        choices=SampleBoxVariantEnum.choices(),
        blank=True,
        null=True,
    )
    objects = ItemDiscountManager()

    def __str__(self):
        display_name = (
            self.service_type
            if self.service_type
            else self.get_valid_discounts_display()
        )
        return f'{self.value}% ({display_name})'

    def get_valid_discounts_display(self) -> str:
        return ', '.join(
            [
                self.get_filter_field_key_value_display(filter_field)
                for filter_field in self.FILTER_FIELDS
                if getattr(self, filter_field) not in {None, ''}
            ]
        )

    def get_filter_field_key_value_display(self, filter_field) -> str:
        """
        Returns for example:
        furniture type: jetty
        is cheapest item: True
        shelf type: TYPE02
        """
        try:
            value = getattr(self, f'get_{filter_field}_display')()
        except AttributeError:
            value = getattr(self, filter_field, None)
        return f'{filter_field.replace("_", " ")}: {value}'

    def calculate_price(self, price) -> Decimal:
        if self.kind_of == VoucherType.ABSOLUTE:
            # note - this is never called when ABSOLUTE, so may be removed
            return max(price - Decimal(self.value), 0)
        return max(price - Decimal(self.value / 100) * price, 0)

    def check_conditions(self, item: SellableItemAbstract) -> bool:
        """If all filter fields are the same at self and item - then the discount is
        valid. In other words - filters are validated on AND logic.
        If ItemDiscount is about a service - it's not counted for one item, but for
        whole order.
        """
        if self.service_type:
            return False
        for filter_field in self.FILTER_FIELDS:
            if getattr(self, filter_field) in {None, ''}:
                # if instance does not store value in given field - skip
                continue
            try:
                if getattr(item, filter_field) != getattr(self, filter_field):
                    # if discount value is different from item quality -
                    # discount is not valid
                    return False
            except AttributeError:
                # item might be a jetty, watty or sample box, and they vary in fields
                # we're filtering on. So if we want to discount box_variant this should
                # not apply to jetty, etc.
                return False
        return True

    def check_condition_for_delivery(self) -> bool:
        return self.service_type == ServiceType.DELIVERY


class VoucherBarterDeal(models.Model):
    voucher = models.OneToOneField(
        Voucher,
        on_delete=models.CASCADE,
        related_name='barter_deal',
    )
    value = models.DecimalField(
        max_digits=6,
        decimal_places=0,
    )
    currency = models.ForeignKey(
        'regions.Currency',
        on_delete=models.CASCADE,
    )

    class Meta:
        verbose_name = 'Barter deal'
        verbose_name_plural = 'Barter deals'

    def __str__(self):
        return (
            f'Barter deal for {self.voucher.code} - {self.value} {self.currency.code}'
        )
