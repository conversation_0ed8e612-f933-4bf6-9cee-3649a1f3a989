import contextlib
import dataclasses
import logging
import time

from functools import wraps
from typing import (
    TYPE_CHECKING,
    List,
    Literal,
    Optional,
    Union,
)
from uuid import UUID

from django.utils import timezone

import requests

from requests import Response
from rest_framework.status import (
    HTTP_409_CONFLICT,
    HTTP_418_IM_A_TEAPOT,
)

from custom.enums import (
    ColorEnum,
    LanguageEnum,
    ShelfType,
    Type02Color,
)
from custom.metrics import metrics_client
from producers.enums import Manufacturers
from producers.production_system_utils.enums import (
    FileType,
    PSConnectionSettings,
)

if TYPE_CHECKING:
    from producers.models import (
        Product,
        ProductBatch,
    )

logger = logging.getLogger('producers')


def get_default_manufacturer_id(
    shelf_type: ShelfType,
    material: ColorEnum,
) -> int:
    if shelf_type == ShelfType.TYPE01:
        return Manufacturers.DREWTUR
    if shelf_type == ShelfType.VENEER_TYPE01:
        return Manufacturers.DREWTUR
    if shelf_type == ShelfType.TYPE02:
        manufacturers: dict[ColorEnum, Manufacturers] = {
            Type02Color.MUSTARD_YELLOW: Manufacturers.STUDIO_93,
            Type02Color.MATTE_BLACK: Manufacturers.MEBLE_PL,
            Type02Color.SKY_BLUE: Manufacturers.MEBLE_PL,
            Type02Color.BURGUNDY: Manufacturers.STUDIO_93,
            Type02Color.COTTON: Manufacturers.STUDIO_93,
        }
        return manufacturers.get(material, Manufacturers.DREWTUR)
    if shelf_type == ShelfType.TYPE03:
        return Manufacturers.AMIR
    if shelf_type == ShelfType.TYPE13:
        return Manufacturers.AMIR
    if shelf_type == ShelfType.VENEER_TYPE13:
        return Manufacturers.AMIR
    if shelf_type in {
        ShelfType.TYPE23,
        ShelfType.TYPE24,
        ShelfType.TYPE25,
    }:
        return Manufacturers.AMIR
    if shelf_type == ShelfType.SOFA_TYPE01:
        return Manufacturers.GALA
    raise ValueError(f'Unknown shelf type: {shelf_type}')


def ps_client_metrics(fun):
    @wraps(fun)
    def _decorator(*args, **kwargs):
        tags = [f'method_name:{fun.__name__}']
        start_time = time.time()
        try:
            result = fun(*args, **kwargs)
        except requests.HTTPError:
            tags.append('status:response_http_error')
            raise
        except Exception:
            tags.append('status:error')
            raise
        else:
            tags.append('status:success')
            return result
        finally:
            metrics_client().timing(
                'backend.ps_client.request', time.time() - start_time, tags=tags
            )

    return _decorator


@dataclasses.dataclass
class PSProduct:
    gallery_id: int
    gallery_json: dict
    furniture_type: str
    product_id: Optional[int] = None
    order_id: Optional[int] = None
    manufacturer_id: Optional[int] = None
    complaint_id: Optional[int] = None
    complaint_product_id: Optional[int] = None
    complaint_elements: Optional[List[List[str]]] = None
    complaint_fittings: Optional[List[dict]] = None

    def __post_init__(self):
        if not self.manufacturer_id or self.manufacturer_id == -1:
            self.manufacturer_id = get_default_manufacturer_id(
                self.gallery_json['shelf_type'],
                self.gallery_json['material'],
            )

    def to_dict(self):
        data_dict = {
            field.name: getattr(self, field.name)
            for field in dataclasses.fields(self)
            if getattr(self, field.name, None) is not None
        }
        # PS uses key `manufacturer` for manufacturer id
        data_dict['manufacturer'] = data_dict['manufacturer_id']
        return data_dict


class ProductionSystemClient(object):
    non_critical_error_status = HTTP_418_IM_A_TEAPOT

    def __init__(
        self,
        suppress_errors=True,
    ):
        self.conn = requests.Session()
        self.conn.headers.update(
            {'Authorization': 'Token {0}'.format(PSConnectionSettings.TOKEN)}
        )
        self.suppress_errors = suppress_errors

    def __enter__(self):
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        self.conn.close()

    def _handle_response(self, resp, product: PSProduct):
        try:
            return resp.json()['serialized_shelf']
        except KeyError:
            return self._handle_exception(
                str(resp),
                product,
                'Unknown response from production system: {} using settings {}',
            )

    def _handle_exception(
        self,
        exception,
        product: Optional[PSProduct] = None,
        message_template='Cant connect to production system: {} using settings {}',
    ) -> dict:
        if not self.suppress_errors:
            raise exception
        return {
            'created_at': timezone.now().strftime('%Y-%m-%d %H:%M:%S'),
            'item_type': 'Ivy',
            'shelf_type': '',
            'id_manufactor': product.manufacturer_id if product else None,
            'id_gallery': product.gallery_id if product else None,
            'id_production': product.product_id if product else None,
            'id_pricing_factors': 0,
            'gallery_parameters': {},
            'version': '',
            'errors': [
                message_template.format(str(exception), str(PSConnectionSettings))
            ],
            'item': {},
            'materials': {},
            'margins': {
                'cogs_dict_elements': {},
                'cogs_dict_mat_categories': {},
                'm1_info': {},
            },
        }

    def _handle_production_files_exception(
        self,
        exception,
        batch: Optional['ProductBatch'] = None,
    ) -> None:
        if batch:
            batch.actions_needed_flow.process_batch_files_generation_problem()
        if not self.suppress_errors:
            raise exception

    def _validate_response(self, response: Response) -> None:
        if response.status_code != self.non_critical_error_status:
            response.raise_for_status()

    def product_serialize(self, product: PSProduct, save: bool = False):
        """Serialize gallery JSON to production JSON using Production System."""
        try:
            if save:
                resp = self._post_serialize(product)
            else:
                resp = self._post_dry_run(product)
        except (requests.HTTPError, requests.ConnectionError) as e:
            logger.critical(
                'Cant connect to production system while serializing product: %s',
                str(e),
                exc_info=True,
            )
            message_template = {
                HTTP_409_CONFLICT: (
                    'There is already a product with the same product ID, '
                    + 'but different gallery ID in PS: {} using settings {}'
                ),
            }.get(
                e.response.status_code if e.response else None,
                'Cant connect to production system: {} using settings {}',
            )
            return self._handle_exception(e, product, message_template)

        return self._handle_response(resp, product)

    @ps_client_metrics
    def _post_serialize(self, product):
        resp = self.conn.post(
            url=PSConnectionSettings.SERIALIZE,
            json=product.to_dict(),
            timeout=PSConnectionSettings.DEFAULT_TIMEOUT,
        )
        self._validate_response(resp)

        return resp

    @ps_client_metrics
    def _post_dry_run(self, product):
        resp = self.conn.post(
            url=PSConnectionSettings.DRY_RUN,
            json=product.to_dict(),
            timeout=PSConnectionSettings.DEFAULT_TIMEOUT,
        )
        self._validate_response(resp)

        return resp

    def _get_manufacturer_id(
        self,
        product_or_batch: Union['Product', 'ProductBatch'],
        manufacturer_id: Optional[int] = None,
    ) -> int:
        if manufacturer_id and manufacturer_id != -1:
            return manufacturer_id
        if product_or_batch.manufactor:
            return product_or_batch.manufactor.id
        try:
            product = product_or_batch.batch_items.first()
        except AttributeError:
            product = product_or_batch

        shelf_type = product.order_item.order_item.shelf_type
        material = product.cached_material
        return get_default_manufacturer_id(shelf_type, material)

    def get_product_test_files(
        self,
        product: 'Product',
        file_type: str,
        manufacturer_id: Optional[int] = None,
        additional_data: Optional[object] = None,
    ) -> Union[bytes, None]:
        """
        Fetches production files of specified type for a single Product.

        Does not depend on product being present in PS database. Cached
        serialization is sent instead.
        """

        url = PSConnectionSettings.PRODUCT_TEST_FILES
        manufacturer = self._get_manufacturer_id(product, manufacturer_id)
        cached_serialization = product.details.cached_serialization.copy()
        cached_serialization['id_manufactor'] = manufacturer
        data = {
            'shelf_serialized': cached_serialization,
            'product': product.id,
            'manufacturer': manufacturer,
            'file_types': file_type,
            'flat': True,
            'additional_data': additional_data or {},
        }
        response = self.conn.post(url=url, json=data)

        try:
            response.raise_for_status()
        except requests.HTTPError as exception:
            logger.critical(
                'Cant connect to production system'
                + 'while getting %s files for Product %s',
                file_type,
                product.id,
                exc_info=True,
            )
            return self._handle_production_files_exception(exception)

        return response.content

    def get_gallery_test_files(
        self,
        gallery_json: dict,
        furniture_type: Literal['jetty', 'watty'],
        file_type: str,
    ) -> Union[bytes, None]:
        """
        Fetches production files of specified type for a single Jetty or watty.

        Does not depend on product being present in PS database. Test
        serialization is obtained first and then sent back to PS to generate files.
        """
        url = PSConnectionSettings.PRODUCT_TEST_FILES
        ps_product = PSProduct(
            gallery_id=gallery_json['id'],
            gallery_json=gallery_json,
            furniture_type=furniture_type,
            product_id=-1,
        )
        cached_serialization = self.product_serialize(ps_product, save=False)
        data = {
            'shelf_serialized': cached_serialization,
            'product': -1,
            'manufacturer': ps_product.manufacturer_id,
            'file_types': file_type,
            'flat': True,
        }
        response = self.conn.post(url=url, json=data)

        try:
            response.raise_for_status()
        except requests.HTTPError as exception:
            logger.critical(
                'Cant connect to production system'
                + 'while getting %s files for Product %s',
                file_type,
                gallery_json['id'],
                exc_info=True,
            )
            return self._handle_production_files_exception(exception)

        return response.content

    def get_order_file_summary(self, batches):
        """Fetches Wardrobes order file for batch queryset."""
        data = {
            'batches': [
                {
                    'id': batch.id,
                    'material': batch.material_description,
                    'products': list(batch.batch_items.values_list('pk', flat=True)),
                    'batch_type': batch.batch_type,
                }
                for batch in batches
            ],
        }
        url = PSConnectionSettings.ORDER_FILE
        response = self.conn.post(url=url, json=data)

        try:
            response.raise_for_status()
        except requests.HTTPError as e:
            logger.critical(
                'Cant connect to production system while '
                'getting order file: {}'.format(str(e))
            )
            return self._handle_production_files_exception(e)

        return response.content

    @ps_client_metrics
    def request_batch_file_from_ps(
        self,
        batch: 'ProductBatch',
        file_type: str,
        request_id: UUID,
        logistic_data: Optional[dict] = None,
        manufacturer_id: Optional[int] = None,
    ) -> None:
        """Requests production files of specified type for a Batch."""

        url = PSConnectionSettings.BATCH_FILES
        callback_url = PSConnectionSettings.CSTM_CALLBACK_URL
        products = list(batch.batch_items.values_list('id', flat=True))
        manufacturer = self._get_manufacturer_id(batch, manufacturer_id)
        data = {
            'products': products,
            'manufacturer': manufacturer,
            'batch_type': batch.batch_type,
            'batch_id': batch.id,
            'file_types': file_type,
            'request_id': str(request_id),
            'callback_url': callback_url,
            'additional_data': self.get_additional_post_data_batch(batch, file_type),
            'products_priority': self._get_product_priorities(batch),
        }
        print(data['products_priority'])
        response = self.conn.post(url=url, json=data)
        response.raise_for_status()

    @classmethod
    def _get_product_priorities(cls, batch):
        products = batch.batch_items.all()
        return {product.id: product.merged_priorities for product in products}

    def get_additional_post_data_batch(
        self,
        batch: 'ProductBatch',
        file_type: str,
    ) -> dict:
        if file_type == FileType.LABELS_PACKAGING:
            return {'logistic_data': batch.collect_logistic_data()}
        return {}

    def get_additional_post_data_product(
        self,
        product: 'Product',
        file_type: str,
        anonymous=False,
    ) -> dict:
        if file_type == FileType.FRONT_VIEW_PDF:
            if product.display_notes_on_front_view:
                return {
                    'notes': product.notes,
                    'batch_id': product.batch_id or '',
                }
        if file_type == FileType.MANUAL:
            owner = product.order.full_name
            if anonymous:
                owner = 'Tylko' if product.product_type == 'watty' else ''
            return {
                'language_code': (
                    product.order.owner.profile.language or LanguageEnum.EN
                ),
                'owner': owner,
            }
        if file_type == FileType.LABELS_PACKAGING:
            return {
                'logistic_data': {product.id: product.collect_product_logistic_data()}
            }
        return {}

    @ps_client_metrics
    def request_product_file_form_ps(
        self,
        product: 'Product',
        file_type: str,
        request_id: UUID,
        manufacturer_id: Optional[int] = None,
        anonymous=False,
    ) -> None:
        """Requests production files of specified type for a Product."""
        url = PSConnectionSettings.PRODUCT_FILES
        callback_url = PSConnectionSettings.CSTM_CALLBACK_URL
        manufacturer = self._get_manufacturer_id(product, manufacturer_id)
        data = {
            'product': product.id,
            'manufacturer': manufacturer,
            'file_types': file_type,
            'request_id': str(request_id),
            'callback_url': callback_url,
            'additional_data': self.get_additional_post_data_product(
                product,
                file_type,
                anonymous,
            ),
        }
        response = self.conn.post(url=url, json=data)
        try:
            response.raise_for_status()
        except requests.HTTPError:
            from gallery.serializers import furniture_serializer_class_factory

            url = PSConnectionSettings.PRODUCT_FILES
            furniture = product.order_item.order_item
            serializer_class = furniture_serializer_class_factory(furniture)
            serialized_item = serializer_class(furniture).data
            data['furniture'] = serialized_item
            response = self.conn.post(url=url, json=data)
        response.raise_for_status()

    def price_factors_create(self, pricing_factors_serialized_data):
        response = self.conn.post(
            url=PSConnectionSettings.PRICE_FACTORS,
            json={'serialized_data': pricing_factors_serialized_data},
            timeout=PSConnectionSettings.DEFAULT_TIMEOUT,
        )
        try:
            response.raise_for_status()
        except requests.HTTPError as exception:
            logger.critical(
                'Cant connect to production system while creating Price Factors:',
                exc_info=True,
            )
            if not self.suppress_errors:
                raise exception
        return response

    @ps_client_metrics
    def request_sotty_svg_from_ps(self, gallery_json: dict) -> bytes:
        """
        Request a top view axonometric drawing of a Sofa from PS.

        :param gallery_json: Serialized Sotty instance.
        :return: SVG image as bytes.
        """
        response = self.conn.post(
            url=PSConnectionSettings.SOTTY_SVG,
            json={'sotty': gallery_json},
            timeout=PSConnectionSettings.DEFAULT_TIMEOUT,
        )
        try:
            response.raise_for_status()
        except requests.HTTPError as exception:
            logger.critical(
                'Cant connect to production system while Sotty SVG image:',
                exc_info=True,
            )
            if not self.suppress_errors:
                raise exception
        return response.content


class ProductionSystemClientMixin:
    """
    Mixin allows using ProductionClient instance from outer scope.

    It might be useful for keeping one session during operation consisting of multiple
    requests.

    Client from outer scope can be set using context manager defined by this mixin:

    ```
    with instance.using_ps_client(ProductionSystemClient):
        instance.method_using_ps_client()
        instance.other_method_using_ps_client()
    ```

    Methods accessing PS must use `self._ps_client` for above contextmanager to work.

    ATTENTION: Do not use `_ps_client` as contextmanager - it will close connection
    in client and won't be usable for next calls. You should either make sure that
    `_ps_client` has instance assigned or use `_get_ps_client_context` method that
    returns safe context manager for existing `_ps_client` or creates new instance of
    `ProductionSystemClient`.
    """

    _ps_client: Optional[ProductionSystemClient] = None

    @contextlib.contextmanager
    def using_ps_client(self, client: ProductionSystemClient):
        """Contextmanager saving client from outer scope and clearing it afterwards."""
        self._ps_client = client
        try:
            yield self
        finally:
            self._ps_client = None

    def _get_ps_client_context(self, *args, **kwargs):
        """
        Method creates context manager using `_ps_client` or instantiating new client.

        Arguments passed to this method will be passed to `ProductionSystemClient`
        constructor if no `_ps_client` is available. When `_ps_client` is set arguments
        passed to this method WON'T affect existing client.
        """
        if self._ps_client:
            return contextlib.nullcontext(self._ps_client)
        return ProductionSystemClient(*args, **kwargs)


def get_active_price_factors_from_ps():
    headers = {'Authorization': 'Token {0}'.format(PSConnectionSettings.TOKEN)}
    url = PSConnectionSettings.ACTIVE_PFS
    response = requests.get(url, headers=headers)
    try:
        response.raise_for_status()
    except requests.HTTPError:
        logger.error(
            'Cannot get active price factors from production system', exc_info=True
        )

    return response.json()
