from unittest.mock import patch

import pytest

from producers.gh.moduly_glowne.ivy_elements import Pack
from producers.services.cover_packaging import CoverPackagingService


class MockPack(Pack):
    def __init__(self, product):
        self.dim_x = 200
        self.dim_y = 200
        self.dim_z = 200
        self.no_levels = 1
        self.weight = 1.5
        self.pack_id = 1
        self.all_elements = [
            {'name': 'Element1', 'ELEM_TYPE': 'BOARD', 'package_info': {'pack_id': 1}},
        ]
        self.product = product
        self.adjusted_weight = 2
        self.is_group_pack = False
        self.product_ids = []


@pytest.mark.django_db
@patch('producers.services.cover_packaging.CoverPackagingService.get_unwrap_packages')
def test_packaging_in_foliage(unwrap_packages_getter_mock, product):
    unwrap_packages_getter_mock.return_value = [MockPack(product)]
    service = CoverPackagingService([product])
    packages = service.get_packing()
    assert len(packages) == 1


@pytest.mark.django_db
@pytest.mark.parametrize(
    'product_count, expected_boxes', [(2, 1), (4, 1), (5, 2), (8, 2), (9, 3)]
)
@patch('producers.services.cover_packaging.CoverPackagingService.get_unwrap_packages')
def test_packaging_in_boxes(
    unwrap_packages_getter_mock, product_factory, product_count, expected_boxes
):
    products = product_factory.create_batch(product_count)
    unwrap_packages_getter_mock.return_value = [
        MockPack(product) for product in products
    ]
    service = CoverPackagingService(products)
    packages = service.get_packing()
    assert len(packages) == expected_boxes
    assert all(package.is_group_pack for package in packages)
