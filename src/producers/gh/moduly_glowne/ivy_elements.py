from collections import defaultdict
from typing import Optional

from jinja2 import (
    Environment,
    FileSystemLoader,
)
from past.utils import old_div

from custom.enums import PhysicalProductVersion
from producers.enums import Manufacturers
from producers.gh.moduly_export import (
    Ivy_export_BARCODE,
    Ivy_export_shapes,
    Ivy_export_SVG,
)
from producers.gh.moduly_glowne.production_helpers import (
    ProductionHelpers,
    adjust_package_weight_by_manufactor_and_type,
)
from producers.gh.moduly_instrukcja import Ivy_Elements_iso

from . import Ivy_Settings


# ========= OBIEKTY BAZOWE ============================================================
class ElementProduction(Ivy_Elements_iso.IvyElementsIso, ProductionHelpers):
    """Klasa nadrzedna klas elementow."""

    ELEM_TYPE = 'X'

    def __init__(
        self,
        serialized_obj,
        id_manufactor,
        id_production,
        shelf_type=None,
        physical_product_version=None,
    ):
        """Funkcja inicjalizujaca."""
        # --- Deserializacja
        self.ELEM_TYPE = str(serialized_obj['elem_type'])
        self.name = serialized_obj['name']
        self.surname = serialized_obj['surname']
        self.production_name = serialized_obj.get('production_name', '')
        self.name_packaging = serialized_obj['name_packaging']
        self.id_manufactor = id_manufactor
        self.id_production = id_production
        self.shelf_type = shelf_type
        self.module = serialized_obj['module']

        self.y_index = serialized_obj['y_index']
        self.y_index_bottom = serialized_obj['y_index_bottom']

        self.x_domain = serialized_obj['x_domain']
        # TM units precise, can be used for production calculations,
        # need to be converted in safe way to mm at output
        self.y_domain = serialized_obj['y_domain']
        self.z_domain = serialized_obj['z_domain']

        self.weight = serialized_obj['weight']
        self.components = serialized_obj.get('components')

        # -- Informacje pakowania danego obiektu (opisuje położenie elementu w paczce)
        self.package_info = serialized_obj.get('package_info') or {}

        # self.mat_internal_id = serialized_obj["mat_internal_id"]
        # Dodaj wszystkie dodatkowe parametry dotychczas nie zapisane
        self.additional = {
            key: val
            for key, val in list(serialized_obj.items())
            if key.lower() not in list(self.__dict__.keys())
        }

        self.adjacency_list = []
        self.assembly = None
        self.horizontal_above = {}
        self.horizontal_below = {}
        self.fittings = serialized_obj.get('fittings') or []
        self.physical_product_version = physical_product_version
        self.subtype = serialized_obj.get('subtype') or ''

    def __repr__(self):
        return '<{}-{}_{}|{}>'.format(
            self.name,
            self.y_index,
            self.y_index_bottom or 'X',
            self.package_info.get('pack_id', 'X'),
        )

    def __eq__(self, other):
        return str(self) == str(other)

    @property
    def full_name(self):
        """Surname (client name) joined with production name."""
        return '{surname}{production_name}'.format(
            surname=self.surname or '',
            production_name=self.production_name,
        )

    @property
    def is_to_be_produced(self):
        """
        Indicates whether the element is part of a complaint if it belongs to
        a complaint-product, otherwise True.
        """

        return self.additional.get('complaint', True)

    def overwrite_components_to_be_produced(self):
        """For elements that should be produced overwrite their `components`.
        Then we can produce only the ones that need to be produced.
        `complaint: True` - it's a complaint and we'll produce this component
        `complaint: False` - it's a complaint but we don't produce this component
        no `complaint` key - it's not a complaint - let's produce everything.
        """
        if not self.is_to_be_produced or not self.components:
            return self
        self.components = [
            component
            for component in self.components
            if component.get('complaint', True)
        ]
        return self

    @property
    def row(self):
        return (
            self.y_index if not self.y_index_bottom else self.y_index_bottom
        )  # Najnizszy rzad na ktorym jest ob

    @property
    def level(self):
        return self.y_index

    @property
    def bottom_level(self):
        return self.y_index_bottom

    @property
    def x_domain_mm(self):
        return self.get_x_domain(unit='mm', to_int=False)

    @property
    def y_domain_mm(self):
        return self.get_y_domain(unit='mm', to_int=True)

    @property
    def thickness(self):
        """
        thickness in tm precise units, drawers return front element thicnkness
        :return:
        """
        # TODO do zaktulizowania grubosc w zaleznosci od
        if self.ELEM_TYPE in 'BSD':
            thickness = self.z_domain[1] - self.z_domain[0]
        elif self.ELEM_TYPE == 'H':
            thickness = self.y_domain[1] - self.y_domain[0]
        elif self.ELEM_TYPE == 'V':
            thickness = self.x_domain[1] - self.x_domain[0]
        elif self.ELEM_TYPE == 'T':
            # in case od drawer the thickness is the thickness of drawer front part
            try:
                thickness = self.get_components_netto_dim(get_main=True, unit='tm')[0][
                    2
                ]
            except IndexError:
                # it's a complaint and front is not to be produced:
                thickness = 0
        else:
            # why isn't that not enough always?
            return min(
                (
                    self.x_domain[1] - self.x_domain[0],
                    self.y_domain[1] - self.y_domain[0],
                    self.z_domain[1] - self.z_domain[0],
                )
            )
        return thickness

    @property
    def thickness_mm(self):
        return self.unit_converter(self.thickness, to_unit='mm', to_int=False)

    def get_fitting_by_name(self, fitting_name):
        return [
            fitting
            for fitting in self.additional.get('fittings', [])
            if fitting.get('name') == fitting_name
        ]

    def get_x_domain(self, unit='mm', to_int=True):
        return [
            self.unit_converter(self.x_domain[0], to_unit=unit, to_int=to_int),
            self.unit_converter(self.x_domain[1], to_unit=unit, to_int=to_int),
        ]

    def get_y_domain(self, unit='mm', to_int=True):
        return [
            self.unit_converter(self.y_domain[0], to_unit=unit, to_int=to_int),
            self.unit_converter(self.y_domain[1], to_unit=unit, to_int=to_int),
        ]

    def get_z_domain(self, unit='mm', to_int=True):
        return [
            self.unit_converter(self.z_domain[0], to_unit=unit, to_int=to_int),
            self.unit_converter(self.z_domain[1], to_unit=unit, to_int=to_int),
        ]

    def get_lenght(self):
        """Zwraca dlugosc elementu w jednostakch TM."""
        return self.y2_tm - self.y1_tm

    def get_area(self):
        """Zwraca powierzchnie elementu."""
        # FIXME: COKOLWIEK?!
        dimensions = self.get_components_netto_dim(get_main=True)[0]
        return dimensions[0] * dimensions[1]

    def get_gcode_name(self, key=None, gcode_type='mpr'):
        if self.id_manufactor in (1, 24, 29):
            barcode, _ = self.get_element_label_name(
                name=self.full_name,
                prod_id=self.id_production,
                id_manufactor=self.id_manufactor,
                shelf_type=self.shelf_type,
            )
            return '{gcode_type}/{barcode}.{gcode_type}'.format(
                gcode_type=gcode_type,
                barcode=barcode,
            )
        elif self.ELEM_TYPE in 'HBDV' and not key:
            return (
                '{gcode_type}/{shelf_type}-{shelf_id}-{element_full_name}.{gcode_type}'
            ).format(
                gcode_type=gcode_type,
                element_full_name=self.full_name,
                shelf_id=self.id_production,
                shelf_type=['T1', 'T2', 'F1'][self.shelf_type],
            )
        # TODO: this is madness - doesn't work for MPL, because of the if above
        #       if anyone tries to fix it, adjust elements.csv method
        elif self.ELEM_TYPE == 'H' and key == 'lamello':
            return (
                '{gcode_type}/'.format(gcode_type=gcode_type)
                + str(self.id_production)
                + '-'
                + self.full_name
                + '-CNC.{gcode_type}'.format(gcode_type=gcode_type)
            )
        else:
            return ''

    def get_components_nesting_dim(
        self,
        filtery_by_mat_codename=False,
        filter_by_type=False,
        unit='mm',
        round_to_int=True,
        add_additional_element_informations=None,
        add_additional_component_informations=None,
        get_main=False,
    ):
        """
        Funkcja zwraca tupla z wymiarami netto komponentów
        (faktyczne, docelowe wymiary elementów po wydproukowaniu).

        :param filtery_by_mat_codename:  Zawęża listę komponenów na bazie ich
            'material_codename' - {lista lub string}
        :param filter_by_type: Zawęża listę komponentów na bazie ich 'type'
            - {lista lub string}
        :param unit: Określa jednostę wyjściową ('tm' / 'mm' / 'cm' / 'm')
        :param round_to_int: Zaokrągla wymiary do intów
        :param add_additional_element_informations: dodaje do tupla comp dowolne
            atrybuty elementu {lista lub string}
        :param add_additional_component_informations: Dodaje do tupla comp dowolne
            atrybuty komponetu {lista lub string}
        :return: Lista tupli
        """

        # -- Przekształć inputy na listy
        inputs = dict(
            mat_codename=filtery_by_mat_codename,
            type=filter_by_type,
            elem_info=add_additional_element_informations,
            comp_info=add_additional_component_informations,
            main=get_main,
        )
        inputs = {
            k: [v] if v and type(v) not in (list, tuple) else v
            for k, v in list(inputs.items())
        }
        # -- Zbuduj liste wymiarow abc
        data = []
        for comp in self.components or []:  # Jesli brak komponentów pomiń pętlę
            # Pomiń zgodnie z filtrami
            if inputs['main'] and comp.get('main', 'missing') not in inputs['main']:
                continue
            if (
                inputs['mat_codename']
                and comp.get('material_codename', 'missing')
                not in inputs['mat_codename']
            ):
                continue
            if inputs['type'] and comp.get('type', 'missing') not in inputs['type']:
                continue

            n_x = comp.get('x_nesting_offset', 0)
            n_y = comp.get('y_nesting_offset', 0)
            n_z = comp.get('z_nesting_offset', 0)
            # Kolejność domyślna
            _dimms = [
                max(comp['x_domain']) - min(comp['x_domain']) + 2 * n_x,
                max(comp['y_domain']) - min(comp['y_domain']) + 2 * n_y,
                max(comp['z_domain']) - min(comp['z_domain']) + 2 * n_z,
            ]
            # Dla plecow szuflady zamien kolejnosc (sloje)
            if comp.get('type', 'missing') == 'T_back':
                _dimms = [_dimms[1], _dimms[0], _dimms[2]]
            # Przeniesienie thickness na koniec listy z zachowaniem kolejności
            # pozostałych wymiarów i dostosuj jednostki
            _dimms += [_dimms.pop(_dimms.index(sorted(_dimms)[0]))]
            _dimms = [
                self.unit_converter(d, to_unit=unit, to_int=round_to_int)
                for d in _dimms
            ]
            # Dodaj dodatkowe informacje:
            _info = [
                getattr(self, name, self.additional.get(name, 'missing'))
                for name in inputs['elem_info'] or []
            ]
            _info += [comp.get(name, 'missing') for name in inputs['comp_info'] or []]

            data.append(tuple(_dimms + _info))
        return data

    def get_components_netto_dim(
        self,
        filtery_by_mat_codename=False,
        filter_by_type=False,
        unit='mm',
        round_to_int=True,
        add_additional_element_informations=None,
        add_additional_component_informations=None,
        get_main=False,
    ):
        """
        Funkcja zwraca tupla z wymiarami netto komponentów
        (faktyczne, docelowe wymiary elementów po wydproukowaniu).

        :param filtery_by_mat_codename:  Zawęża listę komponenów na bazie ich
            'material_codename' - {lista lub string}
        :param filter_by_type: Zawęża listę komponentów na bazie ich 'type'
            - {lista lub string}
        :param unit: Określa jednostę wyjściową ('tm' / 'mm' / 'cm' / 'm')
        :param round_to_int: Zaokrągla wymiary do intów
        :param add_additional_element_informations: dodaje do tupla comp dowolne
            atrybuty elementu {lista lub string}
        :param add_additional_component_informations: Dodaje do tupla comp dowolne
            atrybuty komponetu {lista lub string}
        :return: Lista tupli
        """

        # -- Przekształć inputy na listy
        inputs = dict(
            mat_codename=filtery_by_mat_codename,
            type=filter_by_type,
            elem_info=add_additional_element_informations,
            comp_info=add_additional_component_informations,
            main=get_main,
        )
        inputs = {
            k: [v] if v and type(v) not in (list, tuple) else v
            for k, v in list(inputs.items())
        }
        # -- Zbuduj liste wymiarow abc
        data = []
        for comp in self.components or []:  # Jesli brak komponentów pomiń pętlę
            # Pomiń zgodnie z filtrami
            if inputs['main'] and comp.get('main', 'missing') not in inputs['main']:
                continue
            if (
                inputs['mat_codename']
                and comp.get('material_codename', 'missing')
                not in inputs['mat_codename']
            ):
                continue
            if inputs['type'] and comp.get('type', 'missing') not in inputs['type']:
                continue
            # Kolejność domyślna
            _dimms = [
                max(comp['x_domain']) - min(comp['x_domain']),
                max(comp['y_domain']) - min(comp['y_domain']),
                max(comp['z_domain']) - min(comp['z_domain']),
            ]
            # Przeniesienie thickness na koniec listy z zachowaniem kolejności
            # pozostałych wymiarów i dostosuj jednostki
            _dimms += [_dimms.pop(_dimms.index(sorted(_dimms)[0]))]
            _dimms = [
                self.unit_converter(d, to_unit=unit, to_int=round_to_int)
                for d in _dimms
            ]
            # Dodaj dodatkowe informacje:
            _info = [
                getattr(self, name, 'missing') for name in inputs['elem_info'] or []
            ]
            _info += [comp.get(name, 'missing') for name in inputs['comp_info'] or []]

            data.append(tuple(_dimms + _info))
        return data

    def repr_for_csv(self):
        """Reprezentacja (nazwa) elementu do zapisu w csv"""
        return self.surname

    def get_bg(
        self,
        shelf_depth,
        transform_to_XY0=False,
        hori_to_pick=None,
        mirror_bottom=False,
        rows_type=0,
        flip_xy=None,
    ):
        """
        zwraca dict bg z wsp nawiertow w domenie horizontala,
         dla verticala, lub supportu lewego lub prawego
        Dla supportu nazwy bg okreslaja:
        bg_x_top - nawiert na gorze horizontala (styk spod supportu i gora horizontala)
        bg_x_bottom - nawiert na spodzie horizontala
            (styk gora supportu i spod horizontala)
        left support direction - support wsuwany w lewo (patrzac od frontu szafki)
        right support direction - support wsuwany w prawo
        bg = {'bg_top': [x1, y1, x2, y2], 'bg_bottom': [x1, y1, x2, y2]}
        input data is in TM units for precision, output data is in changed units MM

        :param shelf_depth:
        :param transform_to_XY0:
        :param hori_to_pick:
        :param mirror_bottom:
        :param rows_type:
        :param flip_xy: horizontal length and depth for cnc rotation on Z axis when
            lamello on right side only and drewtur is manufacturer
        :return:
        """

        if self.ELEM_TYPE != 'H':
            bg = {}
            transform_top = 0
            transform_bottom = 0

            _h_a_d = (
                self.horizontal_above[hori_to_pick]['x_domain']
                if hori_to_pick in self.horizontal_above
                else [0, 0]
            )
            _h_above_length = _h_a_d[1] - _h_a_d[0]

            _h_b_d = (
                self.horizontal_below[hori_to_pick]['x_domain']
                if hori_to_pick in self.horizontal_below
                else [0, 0]
            )
            _h_below_length = _h_b_d[1] - _h_b_d[0]

            if transform_to_XY0:
                if hori_to_pick in self.horizontal_above:

                    if self.horizontal_above[hori_to_pick]['x_domain'][0] <= 0:
                        transform_top = abs(
                            self.horizontal_above[hori_to_pick]['x_domain'][0]
                        )
                    elif self.horizontal_above[hori_to_pick]['x_domain'][0] > 0:
                        transform_top = (
                            self.horizontal_above[hori_to_pick]['x_domain'][0] * -1
                        )
                if hori_to_pick in self.horizontal_below:

                    if self.horizontal_below[hori_to_pick]['x_domain'][0] <= 0:
                        transform_bottom = abs(
                            self.horizontal_below[hori_to_pick]['x_domain'][0]
                        )
                    elif self.horizontal_below[hori_to_pick]['x_domain'][0] > 0:
                        transform_bottom = (
                            self.horizontal_below[hori_to_pick]['x_domain'][0] * -1
                        )

            bg_top, bg_bottom = self.get_element_bg(
                shelf_depth,
                transform_top=transform_top,
                transform_bottom=transform_bottom,
                mirror_bottom=mirror_bottom,
                rows_type=rows_type,
            )

            # korekta dla nawiertow nie mieszczacych sie w domenie horizontala,
            # np plecow z dwoch modulow
            _bg_top = [x for x in bg_top if 0 <= x[0] <= _h_above_length]
            _bg_bottom = [x for x in bg_bottom if 0 <= x[0] <= _h_below_length]

            # Safe change of units from TM to MM for the proper output to cnc machine
            # NOTE rotate horizontal on Z axis 180 deg for drewtur when lamello
            # on right side (flip_x == horizontal length)
            if not flip_xy:
                flip_xy = [0, 0]
            bg['bg_top'] = [
                [
                    self.unit_converter(
                        abs(x[0] - flip_xy[0]), to_unit='mm', to_int=False
                    ),
                    self.unit_converter(
                        abs(x[1] - flip_xy[1]), to_unit='mm', to_int=False
                    ),
                ]
                for x in _bg_top
            ]
            bg['bg_bottom'] = [
                [
                    self.unit_converter(
                        abs(x[0] - flip_xy[0]), to_unit='mm', to_int=False
                    ),
                    self.unit_converter(
                        abs(x[1] - flip_xy[1]), to_unit='mm', to_int=False
                    ),
                ]
                for x in _bg_bottom
            ]

            return bg
        else:
            print('Horizontal passed as input')

    def get_element_label_name(
        self, name, prod_id=None, id_manufactor=-1, shelf_type=0
    ):
        """
        Returns name for element used for element labels.

        :param name: Name of element
        :param prod_id: Product.id
        :param id_manufactor: Producer.id
        :param shelf_type: Gallery.shelf_type
        :return barcode: string is used for barcode from sticker
            and coresponding cnc file names
        :return barcode_text: string is mostrly used for descritpion
            under barcode on element label
        """
        # shelf code in name only for meblepl producer
        barcode_format = (
            '{shelf_code}-{production_id}-{name}'
            if prod_id == 24
            else '{production_id}-{name}'
        )

        label_format = '{shelf_code} - {production_id} - {name}'

        shelf_code = ['T1', 'T2', 'T1'][shelf_type]

        if self.ELEM_TYPE == 'D' and id_manufactor in {1, 31} and name.startswith('Z'):
            # only for old naming
            name += 'L' if self.direction else 'P'
        barcode = barcode_format.format(
            shelf_code=shelf_code,
            production_id=prod_id,
            name=name,
        )
        barcode_text = label_format.format(
            shelf_code=' '.join(shelf_code),
            production_id=prod_id,
            name=' '.join(
                name
            ),  # rotating text breaks readability of "-" so add spaces between letters
        )
        return barcode, barcode_text

    @classmethod
    def get_vertex_from_domain(
        cls,
        x_domain,
        y_domain,
        z_domain,
        units='mm',
        to_int=False,
        transform=None,
        flip_z=False,
    ):
        """
        get vertex points of bounding box, from xyz domain
        :param x_domain:
        :param y_domain:
        :param z_domain:
        :param units:
        :param to_int:
        :param transform:
        :return:
        """
        flip = 1 if flip_z else 0
        x_domain = (
            cls.unit_converter(x_domain[0], to_unit=units, to_int=to_int)
            + transform[0],
            cls.unit_converter(x_domain[1], to_unit=units, to_int=to_int)
            + transform[0],
        )
        y_domain = (
            cls.unit_converter(y_domain[0], to_unit=units, to_int=to_int)
            + transform[1],
            cls.unit_converter(y_domain[1], to_unit=units, to_int=to_int)
            + transform[1],
        )
        z_domain = (
            cls.unit_converter(z_domain[0], to_unit=units, to_int=to_int)
            + transform[2],
            cls.unit_converter(z_domain[1], to_unit=units, to_int=to_int)
            + transform[2],
        )
        points = {
            'p1': [x_domain[0], y_domain[1], z_domain[abs(1 - flip)]],
            'p2': [x_domain[0], y_domain[0], z_domain[abs(1 - flip)]],
            'p3': [x_domain[1], y_domain[0], z_domain[abs(1 - flip)]],
            'p4': [x_domain[1], y_domain[1], z_domain[abs(1 - flip)]],
            'p5': [x_domain[0], y_domain[1], z_domain[abs(0 - flip)]],
            'p6': [x_domain[0], y_domain[0], z_domain[abs(0 - flip)]],
            'p7': [x_domain[1], y_domain[0], z_domain[abs(0 - flip)]],
            'p8': [x_domain[1], y_domain[1], z_domain[abs(0 - flip)]],
        }
        return points

    @classmethod
    def get_faces_from_vertex(cls, vertex):
        """
        organize vertex into faces in dict structure
        :param vertex:
        :return:
        """
        faces = {
            'front': [vertex['p1'], vertex['p2'], vertex['p3'], vertex['p4']],
            'back': [vertex['p5'], vertex['p6'], vertex['p7'], vertex['p8']],
            'top': [vertex['p1'], vertex['p4'], vertex['p8'], vertex['p5']],
            'bottom': [vertex['p2'], vertex['p3'], vertex['p7'], vertex['p6']],
            'left': [vertex['p1'], vertex['p2'], vertex['p6'], vertex['p5']],
            'right': [vertex['p4'], vertex['p3'], vertex['p7'], vertex['p8']],
        }
        return faces

    def get_element_vertices(
        self, transform=None, filter_by_type=None, reorder_list=None
    ):
        """
        get vertexes for components of elements from domains
        :param transform:
        :param filter_by_type:
        :param reorder_list: list of components types name in order
        :return:
        """
        if transform:
            transform = transform
        else:
            transform = [0, 0, 0, 0]
        pts_comp = []

        if filter_by_type:
            components = [x for x in self.components if x['type'] in filter_by_type]
        else:
            components = self.components

        # HACK for legs get the domains from fittings, used in oldk MANUAL ONLY
        if self.ELEM_TYPE == 'L':
            if self.additional.get('fittings', False):  # if long legs
                components = [
                    {
                        'x_domain': self.additional['fittings'][0]['x_domain'],
                        'y_domain': self.additional['fittings'][0]['y_domain'],
                        'z_domain': self.additional['fittings'][0]['z_domain'],
                    }
                ]
            else:  # if short legs
                components = [
                    {
                        'x_domain': self.x_domain,
                        'y_domain': self.y_domain,
                        'z_domain': self.z_domain,
                    }
                ]

        if reorder_list:
            _r = dict([x['type'], x] for x in components)
            components = [_r[key] for key in reorder_list if _r.get(key, False)]

        for comp in components:
            points = self.get_vertex_from_domain(
                comp['x_domain'],
                comp['y_domain'],
                comp['z_domain'],
                transform=transform,
            )
            pts_comp.append(points)

        return pts_comp


class Horizontal(ElementProduction):
    """Klasa obiektow Horizontals.
    {"y2":0, "y1":0, "x1":-400, "x2":400}"""

    ELEM_TYPE = 'H'

    def __init__(
        self,
        serialized_obj,
        id_manufactor,
        id_production,
        shelf_type,
        physical_product_version,
    ):
        """Funkcja inicjalizujaca."""
        super(Horizontal, self).__init__(
            serialized_obj,
            id_manufactor,
            id_production,
            shelf_type,
            physical_product_version,
        )
        # COORDINATES ----------------------------------
        # TM units precise
        self.x1_tm = self.x_domain[0]
        self.x2_tm = self.x_domain[1]
        self.y1_tm = old_div(sum(self.y_domain), 2)
        self.y2_tm = self.y1_tm
        # MM units not precise
        self.x1 = self.x_domain_mm[0]
        self.x2 = self.x_domain_mm[1]
        self.y1 = old_div(sum(self.y_domain_mm), 2)
        self.y2 = self.y1
        # ----------------------------------------------
        self.assembly = 1 if self.level == 0 else self.level * 3 - 1
        self.depth = self.z_domain[1] - self.z_domain[0]

        self.parts_above = []
        self.parts_below = []
        self.horizontal_connected = []  # polaczony czolowo z horizontalem o nazwie

    # None - NIEUSTALONE!, 0 - Brak laczenia, 1 - Laczenie bez kluczyka,
    # 2 - Kluczyk z dolu, 3 - Kluczyk z gory
    @property
    def joint_left(self):
        return self.additional.get('joint_left', None)

    @property
    def joint_right(self):
        return self.additional.get('joint_right', None)

    def get_lenght(self):
        """Zwraca dlugosc elementu."""
        return self.x2_tm - self.x1_tm

    def get_horizontal_legs_bg(self, leg_objects, transform=False, flip_xy=None):
        """
        positions of legs in horizontal domain for cnc boring
        :param leg_objects:
        :param transform: move legs postions with horizontal to 0,0
        :return:
        """
        legs_in_hori = []
        for leg in leg_objects:
            if (
                leg.x_domain[1] < self.x_domain[1]
                and leg.x_domain[0] > self.x_domain[0]
                and leg.y_index == self.y_index
            ):
                legs_in_hori.append(leg)
        _transform_x = 0
        if transform:
            if self.x1_tm <= 0:
                _transform_x = abs(self.x1_tm)
            elif self.x1_tm > 0:
                _transform_x = -self.x1_tm

        bg = [x.get_leg_bg(transform_x=_transform_x) for x in legs_in_hori]
        if not flip_xy:
            flip_xy = [0, 0]

        flip_xy = [
            self.unit_converter(flip_xy[0], to_unit='mm', to_int=False),
            self.unit_converter(flip_xy[1], to_unit='mm', to_int=False),
        ]
        bg = [[abs(x[0] - flip_xy[0]), abs(x[1] - flip_xy[1])] for x in bg]
        return bg

    def get_element_bg(
        self, shelf_depth, transform_to_XY0=False, mirror_bottom=True, rows_type=0
    ):
        pass


class Vertical(ElementProduction):
    """Klasa obiektow Verticals.
    {"y2":269, "y1":9, "x1":-391, "x2":-391}"""

    ELEM_TYPE = 'V'

    def __init__(
        self,
        serialized_obj,
        id_manufactor,
        id_production,
        shelf_type,
        physical_product_version,
    ):
        """Funkcja inicjalizujaca."""
        super(Vertical, self).__init__(
            serialized_obj,
            id_manufactor,
            id_production,
            shelf_type,
            physical_product_version,
        )
        # COORDINATES ----------------------------------
        # TM units precise
        self.x1_tm = old_div(sum(self.x_domain), 2)
        self.x2_tm = self.x1_tm
        self.y1_tm = self.y_domain[0]
        self.y2_tm = self.y_domain[1]
        # MM units not precise
        self.x1 = old_div(sum(self.x_domain_mm), 2)
        self.x2 = self.x1
        self.y1 = self.y_domain_mm[0]
        self.y2 = self.y_domain_mm[1]
        # ----------------------------------------------

        self.assembly = self.level * 3 - 2
        self.ELEM_TYPE = 'V'

    def _set_coordinates(self, element_dict):
        """Funkcja uzupelnia wspolrzedne obiektu po ich przesunieciu i zesnapowaniu"""

        self.x_domain = element_dict['x_domain']
        self.y_domain = element_dict['y_domain']

        # Okresl wspolrzedne X:
        self.x1 = old_div(
            old_div((element_dict['x_domain'][0] + element_dict['x_domain'][1]), 2), 100
        )
        self.x2 = self.x1

        # Okresl wspolrzedne Y: - znajdz najblizsza wsp Y w obiekcie rzedow do
        # skorygowanej wspolrzednej Y obiektu
        self.y1 = old_div(element_dict['y_domain'][0], 100)
        self.y2 = old_div(element_dict['y_domain'][1], 100)

    def get_element_bg(
        self,
        shelf_depth,
        transform_top,
        transform_bottom,
        mirror_bottom=True,
        rows_type=0,
    ):
        """
        calculate coordinates of boring holes for cnc,
            all dimensions in TM for precision
        :param shelf_depth:
        :param transform_top:
        :param transform_bottom:
        :param mirror_bottom: mirror on X axis bottom horizontal borings for drewtur
        :return:
        """
        # change units from ivy_settings dimensions to TM, for precision
        depth = self.z_domain[1] - self.z_domain[0]

        def get_y_distances(depth_mm, offset):
            """
            get second cnc y distance
            :param depth_mm: shelf depth in mm
            :return:
            """
            y = None

            if depth_mm < 240:
                pass
            elif 240 <= depth_mm <= 310:
                y = depth_mm - 88.6 + offset
            elif depth_mm == 320 or 380 <= depth_mm <= 560 and depth_mm != 520:
                y = offset + 206.0
            elif 330 <= depth_mm <= 370:
                #  forbidden shelf depth_mm, collision
                pass
            elif depth_mm == 520:
                y = offset + 196.0
            elif 570 <= depth_mm <= 700:
                y = offset + 386.0
            return y

        vertical_offset_0_tm = Ivy_Settings.vertical_offset_Y[0] * 100
        vertical_offset_1_tm = (
            get_y_distances(
                self.unit_converter(depth, to_unit='mm', to_int=False),
                Ivy_Settings.vertical_offset_Y[0],
            )
            * 100
        )

        # all dim in TM
        bg_x1_top = self.x1_tm + transform_top
        bg_x2_top = self.x1_tm + transform_top
        bg_x1_bottom = self.x1_tm + transform_bottom
        bg_x2_bottom = self.x1_tm + transform_bottom
        bg_y1_top = vertical_offset_0_tm
        bg_y2_top = vertical_offset_1_tm
        # NOTE mirror if drewtur else meble pl
        bg_y1_bottom = bg_y1_top if mirror_bottom else depth - vertical_offset_0_tm
        bg_y2_bottom = bg_y2_top if mirror_bottom else depth - vertical_offset_1_tm
        bg_top = [[bg_x1_top, bg_y1_top], [bg_x2_top, bg_y2_top]]
        bg_bottom = [[bg_x1_bottom, bg_y1_bottom], [bg_x2_bottom, bg_y2_bottom]]
        return bg_top, bg_bottom

    def get_gcode_name(self, lamello=False, gcode_type='mpr', add_to_name=''):
        return ''.join(
            [
                '{gcode_type}/'.format(gcode_type=gcode_type),
                str(self.id_production),
                '-',
                str(self.full_name),
                add_to_name,
                '.',
                gcode_type,
            ]
        )


class Support(ElementProduction):
    """Klasa obiektow Supports.
    {"y2":269, "y1":9, "x1":-257, "x2":-382, "z1":0, "z2":0}"""

    ELEM_TYPE = 'S'

    def __init__(
        self,
        serialized_obj,
        id_manufactor,
        id_production,
        shelf_type,
        physical_product_version,
    ):
        """Funkcja inicjalizujaca."""
        super().__init__(
            serialized_obj,
            id_manufactor,
            id_production,
            shelf_type,
            physical_product_version,
        )
        # COORDINATES ----------------------------------
        # TM units precise
        self.x1_tm = self.x_domain[0]
        self.x2_tm = self.x_domain[1]
        self.y1_tm = self.y_domain[0]
        self.y2_tm = self.y_domain[1]
        # MM units not precise
        self.x1 = self.x_domain_mm[0]
        self.x2 = self.x_domain_mm[1]
        self.y1 = self.y_domain_mm[0]
        self.y2 = self.y_domain_mm[1]
        # ----------------------------------------------
        self.assembly = self.level * 3

    @property
    def direction(self):
        return self.additional['direction']

    def get_element_bg(
        self,
        shelf_depth,
        transform_top,
        transform_bottom,
        mirror_bottom=True,
        rows_type=0,
    ):
        """
        calculate coordinates of boring holes for cnc,
        all dimensions in TM for precision
        :param shelf_depth:
        :param transform_top:
        :param transform_bottom:
        :param mirror_bottom: mirror bg on bottom side for drewtur,
            top y == bottom y for meblepl
        :return:
        """
        # change units from ivy_settings dimension to TM
        support_offset_X_0_tm = Ivy_Settings.support_offset_X[0] * 100
        support_offset_X_1_tm = Ivy_Settings.support_offset_X[1] * 100
        support_distance_mid_tm = Ivy_Settings.support_distance_mid * 100
        support_offset_Y_tm = Ivy_Settings.support_offset_Y * 100
        support_y_nudge_tm = Ivy_Settings.support_y_nudge * 100

        y = sum(self.z_domain) / 2.0
        # all units in TM
        if self.direction == 'left':
            bg_x1_top = self.x2_tm - support_offset_X_0_tm + transform_top
            bg_x2_top = bg_x1_top - support_distance_mid_tm
            bg_x1_bottom = self.x2_tm - support_offset_X_1_tm + transform_bottom
            bg_x2_bottom = bg_x1_bottom - support_distance_mid_tm
            bg_y1_top = shelf_depth - y - support_offset_Y_tm
            bg_y2_top = shelf_depth - y - support_offset_Y_tm - support_y_nudge_tm
            # HACK bottom and top the same if cnc milling
            # on both sides at once (meblepl)
            bg_y1_bottom = y + support_offset_Y_tm if mirror_bottom else bg_y1_top
            bg_y2_bottom = (
                y + support_offset_Y_tm - support_y_nudge_tm
                if mirror_bottom
                else bg_y2_top
            )
            bg_top = [[bg_x1_top, bg_y1_top], [bg_x2_top, bg_y2_top]]
            bg_bottom = [[bg_x1_bottom, bg_y1_bottom], [bg_x2_bottom, bg_y2_bottom]]

        if self.direction == 'right':
            bg_x1_top = self.x1_tm + support_offset_X_1_tm + transform_top
            bg_x2_top = bg_x1_top + support_distance_mid_tm
            bg_x1_bottom = self.x1_tm + support_offset_X_0_tm + transform_bottom
            bg_x2_bottom = bg_x1_bottom + support_distance_mid_tm
            bg_y1_top = shelf_depth - y - support_offset_Y_tm
            bg_y2_top = shelf_depth - y - support_offset_Y_tm - support_y_nudge_tm
            # HACK bottom and top the same if cnc milling
            # on both sides at once (meblepl)
            bg_y1_bottom = y + support_offset_Y_tm if mirror_bottom else bg_y1_top
            bg_y2_bottom = (
                y + support_offset_Y_tm - support_y_nudge_tm
                if mirror_bottom
                else bg_y2_top
            )
            bg_top = [[bg_x1_top, bg_y1_top], [bg_x2_top, bg_y2_top]]
            bg_bottom = [[bg_x1_bottom, bg_y1_bottom], [bg_x2_bottom, bg_y2_bottom]]

        if not self.direction:
            raise ValueError(
                'Missing direction in support: {name}, {surname}'.format(
                    name=self.name, surname=self.surname
                )
            )
        return bg_top, bg_bottom

    def get_gcode_name(self, bottom=False, gcode_type='mpr'):
        return ''.join(
            [
                '{gcode_type}/'.format(gcode_type=gcode_type),
                str(self.id_production),
                '-',
                str(self.full_name),
                '.',
                gcode_type,
            ]
        )


class Doors(ElementProduction):
    """Klasa drzwi.
    {"y2":267, "x2":21, "y1":11, "x1":-380,
       "z1":320, - wynika z glebokosci szafki, zawsze na rowno z frontem
       "z2":301, - robocza grubosc w dekoderze
       "flip":0, - okresla czy zawiasy sa z 0 - lewej czy z 1 - prawej
       "type":0 - okresla czy uchwyt jest szeroki (typowy - 0)
            czy waski (drzwi podwojne - 1) }"""

    ELEM_TYPE = 'D'

    def __init__(
        self,
        serialized_obj,
        id_manufactor,
        id_production,
        shelf_type,
        physical_product_version,
    ):
        """Funkcja inicjalizujaca"""
        super(Doors, self).__init__(
            serialized_obj,
            id_manufactor,
            id_production,
            shelf_type,
            physical_product_version,
        )
        # COORDINATES ----------------------------------
        # TM units precise
        self.x1_tm = self.x_domain[0]
        self.x2_tm = self.x_domain[1]
        self.y1_tm = self.y_domain[0]
        self.y2_tm = self.y_domain[1]
        # MM units not precise
        self.x1 = self.x_domain_mm[0]
        self.x2 = self.x_domain_mm[1]
        self.y1 = self.y_domain_mm[0]
        self.y2 = self.y_domain_mm[1]
        # ----------------------------------------------
        self.assembly = -1

    @property
    def handle_type(self):
        return self.additional.get('handle', None)

    @property
    def direction(self):
        # 0 - door opening to the right, 1 - door opening to the left
        return self.additional.get('flip', None)

    def get_element_bg(
        self,
        shelf_depth,
        transform_top,
        transform_bottom,
        mirror_bottom=True,
        rows_type=0,
    ):
        """
        calculate coordinates of boring holes for cnc,
        all dimensions in TM for precision
        :param shelf_depth:
        :param transform_top:
        :param transform_bottom:
        :return:
        """
        # TODO uwzglednic
        # change units for ivy_settings dimension to TM
        if not self.shelf_type:  # type 01
            bumper_wide_offset_tm = Ivy_Settings.bumper_wide_offset * 100
            bumper_narrow_offset_tm = Ivy_Settings.bumper_narrow_offset * 100
        else:  # type 02
            bumper_wide_offset_tm = Ivy_Settings.bumper_x_offset_type_02 * 100
            bumper_narrow_offset_tm = bumper_wide_offset_tm

        if self.shelf_type == 2:
            bumper_y_offset_tm = Ivy_Settings.bumper_y_offset_veneer * 100
        else:
            bumper_y_offset_tm = (
                Ivy_Settings.bumper_y_offset[rows_type] * 100
                if not self.shelf_type
                else Ivy_Settings.bumper_y_offset_type_02[self.direction] * 100
            )

        if self.direction == 0:
            if self.handle_type == 0:  # uchwyt szeroki
                bg_x1_bottom = self.x1_tm + bumper_wide_offset_tm + transform_bottom
            elif self.handle_type == 1:  # uchwyt waski
                bg_x1_bottom = self.x1_tm + bumper_narrow_offset_tm + transform_bottom

        elif self.direction == 1:
            if self.handle_type == 0:  # uchwyt szeroki
                bg_x1_bottom = self.x2_tm - bumper_wide_offset_tm + transform_bottom
            elif self.handle_type == 1:
                bg_x1_bottom = self.x2_tm - bumper_narrow_offset_tm + transform_bottom
        bg_y1_bottom = (
            shelf_depth - bumper_y_offset_tm if mirror_bottom else bumper_y_offset_tm
        )
        bg_top = []
        bg_bottom = [[bg_x1_bottom, bg_y1_bottom]]
        return bg_top, bg_bottom


class Drawers(ElementProduction):
    ELEM_TYPE = 'T'

    def __init__(
        self,
        serialized_obj,
        id_manufactor,
        id_production,
        shelf_type,
        physical_product_version,
    ):
        """Funkcja inicjalizujaca"""
        # --- Dane uzupelniane przy tworzeniu obiektu
        super(Drawers, self).__init__(
            serialized_obj,
            id_manufactor,
            id_production,
            shelf_type,
            physical_product_version,
        )
        # COORDINATES ----------------------------------
        # TM units precise
        self.x1_tm = self.x_domain[0]
        self.x2_tm = self.x_domain[1]
        self.y1_tm = self.y_domain[0]
        self.y2_tm = self.y_domain[1]
        # MM units not precise
        self.x1 = self.x_domain_mm[0]
        self.x2 = self.x_domain_mm[1]
        self.y1 = self.y_domain_mm[0]
        self.y2 = self.y_domain_mm[1]
        # ----------------------------------------------
        self.assembly = -1

    def get_drawer_wrapper_dimensions(self):
        # -- Dane
        extra_fill = self.package_info.get('additional_fill', 0)
        drawer_box_stack_height = self.package_info.get('drawer_box_stack_height', None)
        drawer_box_width = self.package_info.get('drawer_box_width', None)
        if drawer_box_stack_height is None:
            pack_height = max(self.package_info['z_domain']) - min(
                self.package_info['z_domain']
            )
            return self.calculate_old_wrapper_dimensions(pack_height, extra_fill)
        else:
            return self.calculate_wrapper_dimensions(
                extra_fill, drawer_box_stack_height, drawer_box_width
            )

    def calculate_old_wrapper_dimensions(self, pack_height, extra_fill):
        luz = 10
        sst = next((c for c in self.components if c['type'] == 'T_back'), {}).get(
            'x_domain', (0, 0)
        )
        sst = old_div((max(sst) - min(sst)), 100)
        wymiary = []
        wymiary.append(int(pack_height + 5))
        wymiary.append(int(wymiary[-1] + sst + luz + 5))
        wymiary.append(int(wymiary[-1] + pack_height + 5))
        if extra_fill:
            wymiary.append(int(wymiary[-1] + sst + extra_fill + luz + 10))
            wymiary.append(int(wymiary[-1] + pack_height + 10))
            wymiary.append(int(wymiary[-1] + extra_fill))
            wymiary.append(int(wymiary[-1] + pack_height + 5))
            wymiary.append(int(wymiary[-1] + extra_fill))
            return wymiary

        wymiary.extend(
            [
                int(wymiary[-1] + sst + luz + 10),
                '-',
                '-',
                '-',
                int(wymiary[-1] + sst + luz + 10 + pack_height + 10),
            ]
        )
        return wymiary

    def calculate_wrapper_dimensions(
        self, extra_fill, drawer_box_stack_height, drawer_box_width
    ):
        F100 = 10  # luz na długości elementów
        wymiary = []
        C100 = drawer_box_stack_height  # wysokość stosu [mm]
        D100 = drawer_box_width  # szerokość stosu [mm]
        wymiary.append(int(C100 + 5))
        wymiary.append(int(wymiary[-1] + D100 + F100 + 5))
        wymiary.append(int(wymiary[-1] + C100 + 5))
        if not extra_fill:
            wymiary.extend(
                [
                    int(wymiary[-1] + D100 + F100 + 10),
                    '-',
                    '-',
                    '-',
                    int(wymiary[-1] + D100 + F100 + 10 + C100 + 10),
                ]
            )
            return wymiary
        wymiary.append(int(wymiary[-1] + D100 + extra_fill + F100 + 10))
        wymiary.append(int(wymiary[-1] + C100 + 10))
        wymiary.append(int(wymiary[-1] + extra_fill))
        wymiary.append(int(wymiary[-1] + C100 + 5))
        wymiary.append(int(wymiary[-1] + extra_fill))
        return wymiary

    def get_packaging_descr(self):
        drawer_box_description = self.package_info.get('drawer_box_description', None)
        if drawer_box_description:
            return [
                drawer_box_description.replace('T_', self.surname),
                'owijka: %s' % self.package_info.get('drawer_box_wrapper', ''),
                'rękaw %s: %s [mm] x %s'
                % (
                    self.package_info.get('drawer_box_sleeve_type', ''),
                    self.package_info.get('drawer_box_sleeve_length', ''),
                    self.package_info.get('drawer_box_sleeve_layers', ''),
                ),
                'wariant: %s' % self.package_info.get('drawer_box_variant', ''),
            ]

        typ = (
            'A' if self.y2 - self.y1 < 230 else 'B' if self.y2 - self.y1 < 300 else 'C'
        )
        szer = (
            'w'
            if max(self.package_info['x_domain']) - min(self.package_info['x_domain'])
            < 500
            else 's'
        )
        gl = (
            'p'
            if max(self.package_info['y_domain']) - min(self.package_info['y_domain'])
            < 300
            else 'g'
        )
        comp = self.components

        bok_dict = [x for x in comp if x['type'] == 'T_side1'][0]
        dl_boku = old_div((bok_dict['z_domain'][1] - bok_dict['z_domain'][0]), 100)

        tyl = [x for x in comp if x['type'] == 'T_back'][0]['surname']
        bok = (
            [x for x in comp if x['type'] == 'T_side1'][0]['surname']
            + '('
            + str(dl_boku)
            + ')'
        )
        dno = [x for x in comp if x['type'] == 'T_bottom'][0]['surname']
        front = [x for x in comp if x['type'] == 'T_front'][0]['surname']

        rodzaje = dict(
            Awp=[
                '{bok} + fill / {bok} + {tyl} / {front} + fill / {dno}'.format(
                    bok=bok, tyl=tyl, front=front, dno=dno
                )
            ],
            Awg=[
                '{bok} + fill / {front} + {bok} / {dno} + {tyl}'.format(
                    bok=bok, front=front, dno=dno, tyl=tyl
                )
            ],
            Asp=[
                '{bok} + {bok} + {tyl} / {front} + fill / {dno}'.format(
                    bok=bok, tyl=tyl, dno=dno, front=front
                )
            ],
            Asg=[
                '{dno} + {tyl} / {front} + {bok} + {bok}'.format(
                    dno=dno, tyl=tyl, front=front, bok=bok
                )
            ],
            Bwp=[
                '{tyl} / {bok} / {bok} / {front} / {dno}'.format(
                    tyl=tyl, bok=bok, front=front, dno=dno
                )
            ],
            Bwg=[
                '{tyl} + fill / {bok} + {bok} / {front} + fill / {dno} + fill'.format(
                    tyl=tyl, bok=bok, front=front, dno=dno
                )
            ],
            Bsp=[
                '{tyl} / {bok} + {bok} / {front} / {dno}'.format(
                    tyl=tyl, bok=bok, dno=dno, front=front
                )
            ],
            Bsg=[
                '{tyl} + {bok} + {bok} / {front} + fill / {dno} + fill'.format(
                    tyl=tyl, bok=bok, front=front, dno=dno
                )
            ],
            Cwp=[''],
            Cwg=[
                '{tyl} / {bok} + fill / {bok} + fill /  {front} / {dno} + fill'.format(
                    tyl=tyl, bok=bok, dno=dno, front=front
                ),
            ],
            Csp=[''],
            Csg=[
                '{tyl} / {bok} + {bok} / {front} / {dno} + fill'.format(
                    tyl=tyl, bok=bok, front=front, dno=dno
                )
            ],
        )
        return ' '.join(rodzaje[typ + szer + gl][0].split(' ')[::-1])

    def get_element_bg(
        self,
        shelf_depth,
        transform_top,
        transform_bottom,
        mirror_bottom=True,
        rows_type=0,
    ):
        """
        calculate coordinates of boring holes for cnc,
        all dimensions in TM for precision
        :param shelf_depth:
        :param transform_top:
        :param transform_bottom:
        :return:
        """
        # change units for ivy_settings dimension to TM
        # drawers don't have need any drilling in horizontals
        pass

    def get_gcode(  # TODO orać
        self, gcode_type='mpr', template_path='/src/producers/gh/gcode/', shelf_type=0
    ):
        """
        generate parametric gcode for drawers
        :param gcode_type:
        :param template_path:
        :return: gcodes list for drawer parts
        """
        # We are nly producing gcoces for INEX now with drewtur templates....
        manufactor = '_drewtur'

        types = ['T_front', 'T_back']
        mpr_templates = {
            x: (
                '{gcode_type}_{shelf_type}_drawer_{height_name}_'
                '{drawer_part}{manufactor}.template'
            ).format(
                drawer_part=x.split('_')[1],
                gcode_type=gcode_type,
                manufactor=manufactor,
                height_name=self.additional['height_name'],
                shelf_type=['T1', 'T2', 'F1'][self.shelf_type],
            )
            for x in types
        }
        gcodes = []
        gcodes_names = []

        parts = self.get_components_netto_dim(
            filter_by_type=types,
            add_additional_component_informations=['type', 'surname'],
            round_to_int=False,
        )
        for part in parts:
            # width, height, thickness, part_type = part
            if part[-2] in ['T_side1', 'T_side2']:
                height, width, thickness, part_type, part_surname = part
            else:
                width, height, thickness, part_type, part_surname = part

            if (
                part_type == 'T_bottom' and self.id_manufactor == 24
            ):  # if mebelpl split drawers bottom
                # TODO this is shitty hack in an old shitty function, for now,
                #   better refactor in PS
                for key in ['bottom1', 'bottom2']:
                    _template = f'{gcode_type}_drawer_{key}{manufactor}.template'

                    gcode_template = Environment(
                        loader=FileSystemLoader(template_path)
                    ).get_template(_template)
                    gcode = gcode_template.render(
                        surname=self.surname,
                        height=height,
                        width=width,
                        thickness=thickness,
                        direction=getattr(self, 'direction', None),
                        elem_type=self.ELEM_TYPE,
                        shelf_type=shelf_type,
                    )
                    gcode = gcode.replace('\n', '\r\n')
                    gcodes.append(gcode)
                    gcodes_names.append(
                        self.get_gcode_name(key=key, gcode_type=gcode_type)
                    )
            else:

                gcode_template = Environment(
                    loader=FileSystemLoader(template_path)
                ).get_template(mpr_templates[part_type])
                gcode = gcode_template.render(
                    surname=self.surname,
                    height=height,
                    width=width,
                    thickness=thickness,
                    direction=getattr(self, 'direction', None),
                    elem_type=self.ELEM_TYPE,
                    shelf_type=shelf_type,
                    product_version=self.physical_product_version,
                )
                gcode = gcode.replace('\n', '\r\n')
                gcodes.append(gcode)

            barcode_name, _ = self.get_element_label_name(
                name=part_surname,
                prod_id=self.id_production,
                id_manufactor=self.id_manufactor,
                shelf_type=self.shelf_type,
            )
            gcodes_names.append(
                '{gcode_type}/{barcode_name}.{gcode_type}'.format(
                    barcode_name=barcode_name, gcode_type=gcode_type
                )
            )
        return list(zip(gcodes, gcodes_names))

    def get_gcode_name(self, key=None, gcode_type='mpr'):
        if (
            self.id_manufactor == Manufacturers.MEBLE_PL
            and self.physical_product_version >= PhysicalProductVersion.PTERO
        ):
            return self.get_gcode_name_for_drawer_3_0(key=key)
        key = key or ''
        if 'front' in key or not key:
            return (
                '{gcode_type}/'.format(gcode_type=gcode_type)
                + str(self.id_production)
                + '-'
                + self.full_name
                + '.'
                + gcode_type
            )
        elif 'back' in key:
            return (
                '{gcode_type}/'.format(gcode_type=gcode_type)
                + str(self.id_production)
                + '-'
                + self.full_name
                + '-tyl.{gcode_type}'.format(gcode_type=gcode_type)
            )
        elif 'side1' in key:
            return (
                '{gcode_type}/'.format(gcode_type=gcode_type)
                + str(self.id_production)
                + '-'
                + self.full_name
                + '-bok1.{gcode_type}'.format(gcode_type=gcode_type)
            )
        elif 'side2' in key:
            return (
                '{gcode_type}/'.format(gcode_type=gcode_type)
                + str(self.id_production)
                + '-'
                + self.full_name
                + '-bok2.{gcode_type}'.format(gcode_type=gcode_type)
            )
        elif 'bottom1' in key:
            return (
                '{gcode_type}/'.format(gcode_type=gcode_type)
                + str(self.id_production)
                + '-'
                + self.full_name
                + '-dno1.{gcode_type}'.format(gcode_type=gcode_type)
            )
        elif 'bottom2' in key:
            return (
                '{gcode_type}/'.format(gcode_type=gcode_type)
                + str(self.id_production)
                + '-'
                + self.full_name
                + '-dno2.{gcode_type}'.format(gcode_type=gcode_type)
            )
        elif 'bottom' in key:
            return (
                '{gcode_type}/'.format(gcode_type=gcode_type)
                + str(self.id_production)
                + '-'
                + self.full_name
                + '-dno.{gcode_type}'.format(gcode_type=gcode_type)
            )
        else:
            return ''

    def get_gcode_name_for_drawer_3_0(
        self,
        key: Optional[str] = None,
        gcode_type: str = 'mpr',
    ) -> str:
        gcode_suffixes = {
            'T_front': '1',
            'T_side1': '2',
            'T_side2': '3',
            'T_back': '4',
            'T_bottom': '5',
            None: '1',
        }
        suffix = gcode_suffixes.get(key)
        if suffix is None:
            return ''
        base_gcode_name = '{0}/{1}-{2}-{3}{4}'.format(
            gcode_type,
            self.id_production,
            self.surname,
            suffix,
            self.production_name,
        )
        return f'{base_gcode_name}.{gcode_type}'


class Backs(ElementProduction):
    """Klasa plecow.
    {"y2":269, "y1":9, "x1":-382, "x2":23,
       "z1":12, - robocza grubosc plecow w dekoderze
       "z2":0 - zawsze 0 }"""

    ELEM_TYPE = 'B'

    def __init__(
        self,
        serialized_obj,
        id_manufactor,
        id_production,
        shelf_type,
        physical_product_version,
    ):
        """Funkcja inicjalizujaca"""

        super(Backs, self).__init__(
            serialized_obj,
            id_manufactor,
            id_production,
            shelf_type,
            physical_product_version,
        )
        # COORDINATES ----------------------------------
        # TM units precise
        self.x1_tm = self.x_domain[0]
        self.x2_tm = self.x_domain[1]
        self.y1_tm = self.y_domain[0]
        self.y2_tm = self.y_domain[1]
        # MM units not precise
        self.x1 = self.x_domain_mm[0]
        self.x2 = self.x_domain_mm[1]
        self.y1 = self.y_domain_mm[0]
        self.y2 = self.y_domain_mm[1]
        self.ELEM_TYPE = 'B'
        self.assembly = self.set_assembly()

    def set_assembly(self):
        # we want the charlie backs to be in first assembly step
        if self.production_name.endswith('S'):
            return 1
        return self.level * 3

    @property
    def grommet_type(self):
        # TODO refactor this and use enum for grommet_type
        element_y_middle = (self.y_domain[0] + self.y_domain[1]) / 2
        grommet = [
            fitting for fitting in self.fittings if fitting.get('name') == 'grommet'
        ]
        if not grommet:
            return False
        grommet_y_middle = (grommet[0]['y_domain'][0] + grommet[0]['y_domain'][1]) / 2
        if grommet_y_middle >= element_y_middle:
            return 'high'  # grommet above back center
        elif grommet_y_middle < element_y_middle:
            return 'low'  # grommet below back center

    def get_element_bg(
        self,
        shelf_depth,
        transform_top,
        transform_bottom,
        mirror_bottom=True,
        rows_type=0,
    ):
        """
        calculate coordinates of boring holes for cnc,
        all dimensions in TM for precision
        :param shelf_depth:
        :param transform_top:
        :param transform_bottom:
        :return:
        """
        # change units for ivy_settings dimension to TM
        back_pin_x_offset_tm = Ivy_Settings.back_pin_x_offset[self.shelf_type] * 100
        # if backs are made from chipboard in type 01, different y offset
        # for back cnc than in wood
        if 'chipboard' in self.additional['main_codename']:
            back_pin_y_offset_tm = (
                Ivy_Settings.back_pin_y_offset[self.shelf_type][0] * 100
            )
        else:
            back_pin_y_offset_tm = (
                Ivy_Settings.back_pin_y_offset[self.shelf_type][1] * 100
            )

        bg_x1_bottom = self.x1_tm + back_pin_x_offset_tm + transform_bottom
        bg_x2_bottom = self.x2_tm - back_pin_x_offset_tm + transform_bottom
        bg_x1_top = self.x1_tm + back_pin_x_offset_tm + transform_top
        bg_x2_top = self.x2_tm - back_pin_x_offset_tm + transform_top

        bg_y1_top = shelf_depth - back_pin_y_offset_tm
        bg_y2_top = shelf_depth - back_pin_y_offset_tm
        bg_y1_bottom = back_pin_y_offset_tm if mirror_bottom else bg_y1_top
        bg_y2_bottom = back_pin_y_offset_tm if mirror_bottom else bg_y2_top

        bg_top = [[bg_x1_top, bg_y1_top], [bg_x2_top, bg_y2_top]]
        bg_bottom = [[bg_x1_bottom, bg_y1_bottom], [bg_x2_bottom, bg_y2_bottom]]
        return bg_top, bg_bottom


class Legs(ElementProduction):
    """Klasa nozek na podstawie danych Ivy z JSON'a.
    {"x1":0, "y1":0, "z1":0}
    Jeden obiekt to jedna nozka (sa dwie nozki - dwa obiekty z ta sama pozycja
    x - frontowa i tylna"""

    ELEM_TYPE = 'L'

    def __init__(
        self,
        serialized_obj,
        id_manufactor,
        id_production,
        shelf_type,
        physical_product_version,
    ):  # element_dict, attr):
        """Funkcja inicjalizujaca"""
        # --- Dane uzupelniane przy tworzeniu obiektu
        super(Legs, self).__init__(
            serialized_obj,
            id_manufactor,
            id_production,
            shelf_type,
            physical_product_version,
        )
        # COORDINATES ----------------------------------
        # TM units precise, legs center axis x and y
        self.x1_tm = old_div(sum(self.x_domain), 2)
        self.y1_tm = old_div(sum(self.y_domain), 2)
        # location of leg in horizontal
        self.z1_tm = old_div(sum(self.z_domain), 2)
        # MM units not precise
        self.x1 = old_div(sum(self.x_domain_mm), 2)
        self.x2 = old_div(sum(self.x_domain_mm), 2)
        self.y1 = self.y_domain_mm[0]
        self.y2 = self.y_domain_mm[1]
        self.ELEM_TYPE = 'L'
        self.assembly = 1

    def _set_coordinates(self, element_dict):
        """Funkcja uzupelnia wspolrzedne obiketu po ich przesunieciu i zesnapowaniu"""
        # Okresl wspolrzedne X:
        self.x1 = 0
        # Okresl wspolrzedne Y: - znajdz najblizsza wsp Y w obiekcie rzedow
        # do skorygowanej wspolrzednej Y obiektu
        self.y1 = 0
        self.z1 = 0

    def get_lenght(self):
        """Zwraca dlugosc elementu."""
        return None

    def get_area(self):
        """Zwraca powierzchnie elementu."""
        return 0

    def get_leg_bg(self, transform_x=0):
        """
        return legs coordinates in safe units MM after conversion
        :return:
        """

        return [
            self.unit_converter(self.x1_tm, to_unit='mm', to_int=False)
            + self.unit_converter(transform_x, to_unit='mm', to_int=False),
            self.unit_converter(self.z1_tm, to_unit='mm', to_int=False),
        ]

    @property
    def is_long(self):
        return any(fitting['name'] == 'long_leg' for fitting in self.fittings)


class Inserts(ElementProduction):
    """
    inserts class
    """

    ELEM_TYPE = 'I'

    def __init__(
        self,
        serialized_obj,
        id_manufactor,
        id_production,
        shelf_type,
        physical_product_version,
    ):
        """Funkcja inicjalizujaca"""

        super(Inserts, self).__init__(
            serialized_obj,
            id_manufactor,
            id_production,
            shelf_type,
            physical_product_version,
        )
        # COORDINATES ----------------------------------
        # TM units precise
        self.x1_tm = self.x_domain[0]
        self.x2_tm = self.x_domain[1]
        self.y1_tm = self.y_domain[0]
        self.y2_tm = self.y_domain[1]
        # MM units not precise
        self.x1 = self.x_domain_mm[0]
        self.x2 = self.x_domain_mm[1]
        self.y1 = self.y_domain_mm[0]
        self.y2 = self.y_domain_mm[1]
        self.assembly = self.level * 3
        self.ELEM_TYPE = 'I'

    def get_element_bg(
        self,
        shelf_depth,
        transform_top,
        transform_bottom,
        mirror_bottom=True,
        rows_type=0,
    ):
        """
        calculate coordinates of boring holes for cnc,
        all dimensions in TM for precision
        :param shelf_depth:
        :param transform_top:
        :param transform_bottom:
        :return:
        """
        pass


class Plinth(ElementProduction):
    """Plinth elements class."""

    ELEM_TYPE = 'P'

    def __init__(
        self,
        serialized_obj,
        id_manufactor,
        id_production,
        shelf_type,
        physical_product_version,
    ):  # element_dict, attr):
        """Setup dimentions and element type."""

        super(Plinth, self).__init__(
            serialized_obj,
            id_manufactor,
            id_production,
            shelf_type,
            physical_product_version,
        )
        # COORDINATES ----------------------------------
        # TM units precise
        self.x1_tm = self.x_domain[0]
        self.x2_tm = self.x_domain[1]
        self.y1_tm = self.y_domain[0]
        self.y2_tm = self.y_domain[1]
        # MM units not precise
        self.x1 = self.x_domain_mm[0]
        self.x2 = self.x_domain_mm[1]
        self.y1 = self.y_domain_mm[0]
        self.y2 = self.y_domain_mm[1]
        self.assembly = 1
        self.ELEM_TYPE = 'P'


class Joints(ElementProduction):
    """Klasa laczen"""

    ELEM_TYPE = 'J'

    def __init__(
        self,
        serialized_obj,
        id_manufactor,
        id_production,
        shelf_type,
        physical_product_version,
    ):
        """Funkcja inicjalizujaca"""
        super(Joints, self).__init__(
            serialized_obj,
            id_manufactor,
            id_production,
            shelf_type,
            physical_product_version,
        )

    @property
    def tool(self):  # Kluczyk: 0-leyw dol, 1-lewa gora, 2-prawy dol, 3-prawa gora
        return self.additional.get('tool', None)

    @property
    def hori_left(self):
        return self.additional.get('hori_left', None)

    @property
    def hori_right(self):
        return self.additional.get('hori_right', None)


# ========= OBIEKTY DODATKOWE - PAKOWANIE =============================================
class Pack:
    def __init__(
        self,
        serialized_pack,
        all_elements,
        id_production,
        id_manufactor,
        product_type,
        is_complaint=False,
        outer_pack=None,
        product=None,
    ):
        self.dim_x = serialized_pack['dim_x']
        self.dim_y = serialized_pack['dim_y']
        self.dim_z = serialized_pack['dim_z']
        self.no_levels = serialized_pack['no_levels']
        self.weight = serialized_pack['weight']
        self.pack_id = serialized_pack['pack_id']
        self.typ = serialized_pack['type']
        self.type = serialized_pack['type'].replace('_Paczka_', '').rstrip(':')
        self.content_type = serialized_pack.get('content_type', '')
        self.rotated = serialized_pack[
            'rotated'
        ]  # odpowiada za korzstniejsze ulozenie wykroju paczki na wstedze
        self.product_type = product_type
        self.pack_label_file_name = '{id}-{num}.pdf'.format(
            id=id_production, num=self.pack_id
        )
        self.all_elements = [
            e
            for e in all_elements
            if e.package_info and e.package_info['pack_id'] == self.pack_id
        ]
        # Obiekt z akcesoriami lub None jeśli brak w paczce
        acc = [x for x in self.all_elements if x.ELEM_TYPE == 'ACC']
        self.accessory = acc[0] if acc else None
        self.id_production = id_production
        self.id_manufactor = id_manufactor
        self.is_complaint = is_complaint
        self.outer_pack = outer_pack
        self.product = product
        self.adjusted_weight = adjust_package_weight_by_manufactor_and_type(
            self.weight,
            self.content_type,
            self.id_manufactor,
            self.id_production,
            self.product,
        )
        self.complaint_fittings = None
        if self.typ == 'StaticFittingsPack':
            self.complaint_fittings = serialized_pack['materials_usage']
        self.is_group_pack = False
        self.product_ids = []

    def __repr__(self):
        return '<PACK_{0:<2} | {1:>4}x{2:>4}x{3:>4} | Weight:{4:05.2f}kg>'.format(
            self.pack_id, self.dim_x, self.dim_y, self.dim_z, self.weight
        )

    def _get_accessory_info_list(self):
        """Prepare list of accessory description (pl) for packaging instruction."""
        acc = self.accessory.additional['accessory_list']

        # manual
        acc_info = [
            '{number} x Instrukcja obslugi'.format(number=acc['manual']['amount']),
        ]

        # assembly tool
        tool_amount = 0
        try:
            # nowa serializacja
            tool_amount = acc['assembly_tool']['amount']
        except KeyError:
            # stara serializacja, jezeli nie ma to jest błąd serializacji
            tool_amount = acc['assembly_board']['amount']

        try:
            variant = (
                'z wycieciem' if acc['assembly_tool']['variant'] == 'wrench' else ''
            )
        except KeyError:
            # stara serializacja
            variant = ''
        acc_info.append(
            '{number} x Deseczka montazowa {variant}'.format(
                number=tool_amount,
                variant=variant,
            )
        )

        # wall fittings
        if acc.get('wall_fittings'):
            wall_fittings_color_pl = {
                'white': 'Biale',
                'metal': 'Srebrne',
            }.get(acc['wall_fittings']['color'], 'Czarne')
            acc_info.append(
                '{number} x {color_pl} mocowanie do sciany'.format(
                    number=acc['wall_fittings']['amount'],
                    color_pl=wall_fittings_color_pl,
                )
            )

        # connectors
        if acc.get('connectors'):
            acc_info.append(
                '{number} x Trzpien usztywniajacy elementy poziome'.format(
                    number=acc['connectors']['amount'],
                )
            )
            acc_info.append(
                '{number} x Kluczyk imbusowy czarny 4 mm'.format(
                    number=acc['tool']['amount'],
                )
            )

        # grommets
        if acc.get('grommets'):
            grommet_color_pl = {
                'dusty-pink': 'rozowa T1',
                'yellow': 'zolta T1',
                'classic-red': 'czerwona T1',
                'oak': 'dab T1',
                'ash': 'jesion T1',
                'pastel-green': 'zielona T2',
                'sand-beige': 'bezowa T2',
                'indigo-blue': 'niebieska T2',
                'ceramic-red': 'czerwona T2',
                'snow-white': 'biala T2',
                'black-mat': 'czarna T2',
                'blue': 'jasnoniebieska T2',
                'burgundy': 'burgundowa T2',
                'cotton': 'cotton T2',
                'dark-gray': 'szara T2',
                'yellow-mustard': 'bezowa T2',
                'amazon-green': 'zielona T1',
                'aubergine': 'oberzynowa T1',
                'gray': 'szara T1/T2',
                'black': 'czarna T1',
                'white': 'biala T1',
                'cobalt-blue': 'niebieska T1',
                'dark-brown': 'brazowa T1',
                'green-agava': 'zielona agawa T1',
                'reisinger-pink': 'rozowa T2',
                'sage-green': 'pistacja T2',
                'stone-gray': 'szara kamienna T2',
            }.get(acc['grommets']['color'], '')
            acc_info.append(
                '{number} x Pokrywka magnetyczna do przelotki {color}'.format(
                    number=acc['grommets']['amount'],
                    color=grommet_color_pl,
                )
            )
        return acc_info

    def _get_complaint_fittings_info_list(self):
        fittings = self.complaint_fittings
        # put packaging elements first
        sorted_codenames = sorted(
            fittings.items(),
            key=lambda item: item[0].startswith('packaging_'),
            reverse=True,
        )
        return [
            f'{codename} {usage} szt.'
            for codename, usage in sorted_codenames
            if codename != 'packaging_plastic_white_PP-band'
        ]

    def get_table_data(
        self,
        production_id,
        paczka='1/1',
        color='',
        color_pl='',
        country_pl='',
        shelf_type=None,
        ppv=None,
    ):
        """Zwraca slownik z danymi paczki do eksportu."""
        # Ustawienia
        wys_r = 30  # Wysokosc rekordu
        margines = 8  # Marginesy

        # Tworze listy do slownika z danymi
        tabela_lo = []  # Linie tabeli - OBRYS
        tabela_lw = []  # Linie tabeli - wewnetrzne
        tabela_lz = []  # Linie tabeli - zewnetrzne
        tabela_tw = []  # Teksty tabeli
        tabela_tz = []  # Numeracja poziomow tabeli
        tabela_th = []  # Naglowki
        tabela_f = []  # File
        tab_dr_l = []  # Szuflady linie
        tab_dr_txt = []  # Szuflady teksty

        # Wysokosc rysunku
        elements_by_level = self.get_elements_by_level()
        drawers = [
            element
            for poziom in list(elements_by_level.values())
            for element in poziom
            if element.ELEM_TYPE == 'T'
        ]

        # It can be changed later within the function
        wys_rys = (len(list(elements_by_level.keys())) + 2) * wys_r + 2 * margines

        # Szerokosc rysunku
        szer_rys = 580  # if hori else 350

        # Podstawowe punkty odniesienia
        x1 = margines  # Lewa krawedz
        x2 = x1 + wys_r  # Kolumna numeracji
        x3 = x2 + wys_r  # Kolumna checkbox
        x4 = szer_rys - margines  # Prawa krawedz
        y1 = margines  # Gora tabelki
        y2 = y1 + wys_r + wys_r  # Gora tabelki z elementami paczki
        y3 = (
            y2 + len(list(elements_by_level.keys())) * wys_r
        )  # Dol tabelki z elementami (nad akcesoriami)

        # Szerokosc szafki
        skala_k = (x4 - x3) / float(self.dim_x)
        beaver_bumpers = self.get_beaver_bumpers()
        tubes = self.get_tubes()
        y4 = y3
        # Opis akcesoriow lub fittingów
        acc_info = None
        if self.accessory:
            acc_info = self._get_accessory_info_list()
            # Dodaj opis akcesoriow
            tabela_tz.append(('Akcesoria:', x1 + 6, y3 + 18, 15))
            counter = 0
            for nr, txt in enumerate(acc_info):
                counter += 1
                tabela_tw.append((txt, x1 + 6, y3 + 20 + (14 * (nr + 1)), 12))
            # Dodaj makiete - punkty i linie
            y4 += 22 + counter * 16
            wys_rys += 22 + counter * 16
        if beaver_bumpers:
            bumper_dimensions = self.get_bumper_dimensions(beaver_bumpers)
            tabela_tz.append((f'2x zderzak {bumper_dimensions}', x1 + 6, y4 + 18, 15))
            y4 += 22
            wys_rys += 22
        for diameter, count in tubes.items():
            tabela_tz.append((f'{count}x tuleja ϕ{diameter}', x1 + 6, y4 + 18, 15))
            y4 += 22
            wys_rys += 22
        if self.complaint_fittings:
            fittings_info = self._get_complaint_fittings_info_list()
            for nr, txt in enumerate(fittings_info):
                tabela_tw.append((txt, x1 + 6, y3 + 20 + (14 * (nr + 1)), 12))
            counter = len(fittings_info)
            y4 += 22 + counter * 16
            wys_rys += 22 + counter * 16

        if (
            self.accessory
            or self.complaint_fittings
            or beaver_bumpers
            or tubes[80]
            or tubes[150]
        ):
            tabela_lz.append((x1, y4, x4, y4))  # Linia dolna zamykajaca akcesoria
            tabela_lz.append((x4, y3, x4, y4))  # Linia prawa zamykajaca akcesoria

        # Tytul tabelki
        nr_paczki = paczka
        if self.outer_pack:
            x, y, z = (
                self.outer_pack['dim_x'],
                self.outer_pack['dim_y'],
                self.outer_pack['dim_z'],
            )
        else:
            x, y, z = self.dim_x, self.dim_y, self.dim_z
        waga_paczki = '%.2f kg' % self.weight
        wymiar_paczki = '%.0f x %.0f x %.0f mm' % (x, y, z)

        if len(nr_paczki) > 3:
            tabela_th.append((nr_paczki, x1 + 5, wys_r * 1.2 + margines, wys_r * 0.55))
        else:
            tabela_th.append((nr_paczki, x1 + 5, wys_r * 1.4 + margines, wys_r * 0.85))

        tabela_tz.append(
            ('Wymiar wewnetrzny paczki:', x3 + 5, wys_r * 0.3 + margines, wys_r * 0.25)
        )
        tabela_th.append(
            (wymiar_paczki, x3 + 10, wys_r * 0.65 + margines + 7, wys_r * 0.6)
        )
        if country_pl:
            tabela_tz.append(('Kraj:', x3 + 355, wys_r * 0.3 + margines, wys_r * 0.25))
            tabela_th.append(
                (country_pl, x3 + 360, wys_r * 0.65 + margines + 7, wys_r * 0.6)
            )

        tabela_tz.append(('Waga paczki:', x3 + 5, wys_r * 1.3 + margines, wys_r * 0.25))
        tabela_th.append(
            (waga_paczki, x3 + 10, wys_r * 0.65 + margines + wys_r * 1.2, wys_r * 0.6)
        )

        # Linie stale tabeli

        tabela_lo.append((x3, y2, x4, y2))
        tabela_lo.append((x3, y2, x3, y3))
        tabela_lo.append((x4, y2, x4, y3))
        tabela_lo.append((x3, y3, x4, y3))

        tabela_lz.append((x1, y1, x4, y1))
        tabela_lz.append((x1, y1, x1, y4))
        tabela_lz.append((x2, y2, x2, y3))
        tabela_lz.append((x4, y1, x4, y2))
        tabela_lz.append((x1, y2, x3, y2))
        tabela_lz.append((x3, y1, x3, y2))
        tabela_lz.append((x3, y1 + wys_r, x4, y1 + wys_r))

        # Tworze slownik na dane
        _custom_fill_colors = {
            'dusty-pink': '#dbbebb',
            'aubergine': '#5d484f',
            'natural': '#cccc99',
            'midnight-blue': '#272e3b',
            'ash': '#cccc99',
            'oak': '#ba8f2b',
            'dark-oak': '#6f5339',
            'mat-black': '#000000',
            'blue': '#7D9CC0',
            'burgundy': '#800020',
            'cotton': '#fdf3ea',
            'gray': '#808080',
            'dark-gray': '#404040',
            'yellow-mustard': '#e1ad01',
            'reisinger-pink': '#e101c3',
            'sage-green': '#a3b5a6',
            'stone-gray': '#949494',
            'walnut': '#949494',
            'cobalt-blue': '#1f6ed4',
            'dark-brown': '#4a2e1e',
            'green-agava': '#869c68',
        }
        color = _custom_fill_colors.get(color, color)
        drawing = {
            'drawing': {
                'size': (szer_rys, wys_rys),
                'canvas': (0, 0, szer_rys, wys_rys),  # Def. obszar roboczy
            },
            'graphic': [
                # Wypelnienia
                {
                    'group_data': {
                        'id': 'paczki_fill',
                        'fill': 'lightgrey',
                        'stroke': 'black',
                    },
                    'rect': tabela_f,
                },
                # Teksty tabeli
                {
                    'group_data': {
                        'id': 'paczki_TextWew',
                        'font-family': 'arial',
                        'font-weight': 500,
                    },
                    'text': tabela_tw,
                },
                # Teksty naglowka
                {
                    'group_data': {
                        'id': 'paczki_TextH',
                        'font-family': 'arial',
                        'font-weight': 900,
                    },
                    'text': tabela_th,
                },
                # Linie tabelki zewnetrzne
                {
                    'group_data': {
                        'id': 'paczki_tabLines',
                        'stroke': 'grey',
                        'stroke-width': 2,
                    },
                    'line': tabela_lz,
                },
                # Linie tabeli obrys
                {
                    'group_data': {
                        'id': 'paczki_tabLines',
                        'stroke': 'black',
                        'stroke-width': 2,
                    },
                    'line': tabela_lo,
                },
                # Linie tabelki wewnetrzne
                {
                    'group_data': {
                        'id': 'paczki_tabLinesthin',
                        'stroke': 'black',
                        'stroke-width': 1,
                    },
                    'line': tabela_lw,
                },
                # Linie tabelki szuflad
                {
                    'group_data': {
                        'id': 'paczki_tabLinesthin',
                        'stroke': 'grey',
                        'stroke-width': 1,
                    },
                    'line': tab_dr_l,
                },
                # Teksty tabeli szuflad
                {
                    'group_data': {
                        'id': 'paczki_TextZew',
                        'fill': 'grey',
                        'font-family': 'arial',
                        'font-weight': 300,
                    },
                    'text': tab_dr_txt,
                },
                {
                    'group_data': {'id': 'circle_color', 'fill': 'grey'},
                    # add circle with the fill same color as material,
                    # request from drewtur
                    'circle': [
                        {
                            'center': (x1 + 30, y4 + wys_r),
                            'radius': 22.5,
                            'fill': color,
                            'stroke': 'black',
                            'stroke-width': 2,
                        }
                    ],
                },
                # Teksty tabeli
                {
                    'group_data': {
                        'id': 'paczki_TextZew',
                        'fill': 'grey',
                        'font-family': 'arial',
                        'font-weight': 600,
                    },
                    'text': tabela_tz,
                },
            ],
        }

        # Kod paczki + stempel dla Pagedu
        pack_code = f'{self.id_production}-{paczka.split("/")[0]}'
        tabela_tz.append(
            ('Kod paczki:', x3 + 105, wys_r * 1.3 + margines, wys_r * 0.25)
        )
        tabela_th.append(
            (pack_code, x3 + 110, wys_r * 0.65 + margines + wys_r * 1.2, wys_r * 0.6)
        )
        plywood_info = None
        if 'sklejka' in color_pl and shelf_type == 0:
            if ppv >= PhysicalProductVersion.PACHY:
                plywood_info = 'Sklejka 19mm'
            else:
                plywood_info = 'Sklejka 18mm'
        if plywood_info:
            tabela_th.append(
                (
                    plywood_info,
                    x3 + 360,
                    wys_r * 0.65 + margines + wys_r * 1.2,
                    wys_r * 0.6,
                )
            )
        # Add barcodes
        if self.outer_pack:
            outer_pack_id = self.outer_pack['pack_id']
            barcodes = [
                f'{pack_code}-2',
                f'{self.id_production}-{outer_pack_id}-1',
            ]
        else:
            barcodes = [pack_code]
        fefco_fills = [
            element
            for element in self.all_elements
            if element.ELEM_TYPE == 'FEFCO_FILL'
        ]
        for fill in fefco_fills:
            cartoner_name = fill.additional.get('cartoner_name', '')
            barcodes.append(
                f'{self.id_production}-{self.pack_id}-{cartoner_name}',
            )

        barcode_size = 2 * wys_r
        barcode_margins = 5
        y5 = y4

        for barcode_data in barcodes:
            barcode = Ivy_export_BARCODE.Code39(
                code=barcode_data,
                movex=x3,
                movey=y5 + barcode_margins,
                sizex=x4 - 68,
                sizey=barcode_size,
            )
            drawing['graphic'].append(barcode.get_barcode_dict())
            y5 += barcode_size + 2 * barcode_margins

        # Add frame around barcodes
        tabela_lz.append((x3 + 100, y2 - wys_r, x3 + 100, y2))
        tabela_lz.append((x1, y4, x1, y5))
        tabela_lz.append((x1, y5, x4, y5))
        tabela_lz.append((x4, y4, x4, y5))

        barcodes_section_size = y5 - y4
        wys_rys += barcodes_section_size
        drawing['drawing']['size'] = (szer_rys, wys_rys)
        drawing['drawing']['canvas'] = (0, 0, szer_rys, wys_rys)

        if paczka.split('/')[0] == paczka.split('/')[1]:
            # Dodaj do rysunku
            drawing['drawing']['size'] = (szer_rys, wys_rys + 100)
            drawing['drawing']['canvas'] = (0, 0, szer_rys, wys_rys + 100)

        if drawers:  # Dodatkowa tabelka z wymiarami dla szuflad

            # Podstawowe punkty odniesienia
            yd0 = y5 + wys_r * 0.5
            xd1 = x1 + wys_r * 1.5
            xdl = [xd1 + x * wys_r * 1.1 for x in range(0, 8)]

            current_y = yd0
            for row_id, drawer in enumerate(['Title'] + drawers):
                # Wymiary owijki
                y6 = current_y
                current_y += wys_r
                numery = (
                    drawer.get_drawer_wrapper_dimensions()
                    if row_id != 0
                    else [1, 2, 3, 4, 5, 6, 7, 'Total']
                )
                yt = current_y - wys_r * 0.25

                tab_dr_l.append(
                    (
                        xd1 if row_id != 0 else x1,
                        current_y,
                        (x4 - 135) if row_id != 0 else x4,
                        current_y,
                    )
                )
                tab_dr_txt.append(
                    (
                        str(drawer.surname if row_id != 0 else 'ID'),
                        x1 + 3,
                        yt,
                        wys_r * 0.6,
                    )
                )
                for nr, wymiar in enumerate(numery):
                    tab_dr_txt.append(
                        (
                            wymiar,
                            xdl[nr] + (4 if nr != 7 else 6),
                            yt - (4 if nr != 7 else 3),
                            wys_r * 0.33 if nr != 7 else wys_r * 0.42,
                        )
                    )
                    tab_dr_l.append((xdl[nr], current_y - wys_r, xdl[nr], current_y))

                if row_id == 0:
                    continue

                # Opisy pakowania
                info = drawer.get_packaging_descr()
                current_y += wys_r * 0.5
                tab_dr_txt.append(
                    (info[0], xd1 + 5, current_y - wys_r * 0.1, wys_r * 0.4)
                )
                tab_dr_l.append((x1, current_y, x4, current_y))
                tab_dr_l.append((x4 - 135, y6, x4 - 135, current_y))
                y7, current_y = current_y, y6
                for info_row in info[1:]:
                    current_y += wys_r * 0.5
                    tab_dr_txt.append(
                        (info_row, x4 - 130, current_y - wys_r * 0.1, wys_r * 0.4)
                    )
                current_y = y7

            tab_dr_l.append((x1, yd0, x4, yd0))
            tab_dr_l.append((x1, yd0, x1, current_y))
            tab_dr_l.append((xd1, yd0, xd1, current_y))
            tab_dr_l.append((x1, current_y, x4, current_y))
            tab_dr_l.append((x4, yd0, x4, current_y))

            drawing['drawing'] = {
                'size': (szer_rys, drawing['drawing']['size'][1] + current_y - y5),
                'canvas': (
                    0,
                    0,
                    szer_rys,
                    drawing['drawing']['size'][1] + current_y - y5,
                ),  # Def. obszar roboczy
            }

        # Rekordy tabeli
        for num, key in enumerate(elements_by_level.keys()):
            z = margines + (num + 3) * wys_r  # Wysokosc poziomu
            tabela_lw.append((x3, z, x4, z))  # Linia pozioma wewnetrzna
            tabela_lz.append((x1, z, x3, z))  # Linia pozioma zewnetrzna

            n = len(list(elements_by_level.keys())) - num  # Poziom warstwy
            tabela_tz.append((str(n) + '.', 3 + margines, z - 5, wys_r * 0.5))
            cell = x3  # Biezace polozenie X komorki grupy

            # Dla kazdej komorki
            for num, section_id in enumerate(
                {x.package_info['pack_section'] for x in elements_by_level[key]}
            ):
                item_group = [
                    x
                    for x in elements_by_level[key]
                    if x.package_info['pack_section'] == section_id
                ]
                item_group.sort(key=lambda x: x.package_info['pack_section_item'])
                section_width = (
                    max((x.package_info['x_domain'][1] for x in item_group))
                    - min((x.package_info['x_domain'][0] for x in item_group))
                ) * skala_k
                cell += section_width

                element_width = old_div(section_width, len(item_group))
                _cell = cell  # Biezace polozenie X komorki elementu
                korekta = 0
                for item_nr, item in enumerate(item_group):
                    if (
                        item.ELEM_TYPE == 'FILL'
                        and (item.additional.get('main_codename', 'nan') or 'nan')[:3]
                        == 'nan'
                    ):
                        continue
                    elif item.ELEM_TYPE == 'ACC':
                        _skala = (
                            1 if element_width > 120 else old_div(element_width, 120)
                        )
                        tabela_tw.append(
                            (
                                'Akcesoria',
                                _cell - old_div(element_width, 2) - (55 * _skala),
                                z - 5,
                                wys_r * 0.8 * _skala,
                            )
                        )
                        tabela_f.append(
                            (_cell - element_width, z - wys_r, element_width, wys_r),
                        )
                    elif item.ELEM_TYPE == 'FEFCO_FILL':
                        _skala = 1 if element_width > 50 else old_div(element_width, 50)
                        tabela_tw.append(
                            (
                                item.additional.get('cartoner_name'),
                                _cell - old_div(element_width, 2) - (20 * _skala),
                                z - 5,
                                wys_r * 0.8 * _skala,
                            )
                        )
                        tabela_f.append(
                            (_cell - element_width, z - wys_r, element_width, wys_r),
                        )
                    elif item.ELEM_TYPE == 'LEG_BOX':
                        _skala = 1 if element_width > 120 else element_width / 120
                        tabela_tw.append(
                            (
                                '2 nogi',
                                _cell - element_width / 2 - (35 * _skala),
                                z - 16,
                                wys_r * 0.52 * _skala,
                            )
                        )
                        leg_color = color_pl
                        if leg_color == 'bezowe':
                            leg_color = 'granatowe'
                        tabela_tw.append(
                            (
                                leg_color,
                                _cell - element_width / 2 - (35 * _skala),
                                z - 2,
                                wys_r * 0.52 * _skala,
                            )
                        )
                        tabela_f.append(
                            (_cell - element_width, z - wys_r, element_width, wys_r),
                        )
                    elif item.ELEM_TYPE == 'FILL':
                        korekta = (
                            0 if _cell <= x4 else _cell - x4
                        )  # Albowiem nie możesz być szerszy niż tabela
                        if (
                            item.package_info['y_domain'][1] > 15
                            or item.package_info['x_domain'][1] > 15
                        ):  # Bez wypelnien mniejszych niz 1,5cm
                            na = '%sx' % (
                                int(
                                    item.package_info['y_domain'][1]
                                    - item.package_info['y_domain'][0]
                                )
                            )
                            me = '%s' % (
                                int(
                                    item.package_info['x_domain'][1]
                                    - item.package_info['x_domain'][0]
                                )
                            )
                            _skala = (
                                1
                                if element_width > max(len(na), len(me)) * 12
                                else old_div(
                                    element_width,
                                    (max(len(na), len(me)) * 12),
                                )
                            )
                            na = na if _skala == 1 else na[:-1]
                            tabela_tw.append(
                                (
                                    na,
                                    _cell
                                    - old_div(element_width, 2)
                                    - (len(na) / float(2) * 9 * _skala),
                                    z - 16,
                                    wys_r * 0.52 * _skala,
                                )
                            )
                            tabela_tw.append(
                                (
                                    me,
                                    _cell
                                    - old_div(element_width, 2)
                                    - (len(me) / float(2) * 9 * _skala),
                                    z - 2 - (8 * (1 - _skala)),
                                    wys_r * 0.52 * _skala,
                                )
                            )
                        tabela_f.append(
                            (
                                _cell - element_width,
                                z - wys_r,
                                element_width - korekta,
                                wys_r,
                            ),
                        )
                    elif (
                        len(item.full_name) == 2
                        and item.full_name[1] == 's'
                        and element_width < 50
                    ):
                        # Skalowanie opisow supportow do mniejszych komorek
                        tabela_tw.append(
                            (
                                item.full_name,
                                _cell - element_width + 2,
                                z - 4,
                                wys_r * 0.7,
                            )
                        )
                    elif item.ELEM_TYPE == 'T' and item.package_info.get(
                        'additional_fill'
                    ):
                        # Dodatkowe wypełnienia w paczkach
                        tabela_tw.append(
                            (
                                item.full_name,
                                _cell
                                - element_width / 2
                                - (float(len(item.full_name)) / 2 * 15),
                                z - 4,
                                wys_r,
                            )
                        )
                        continue  # Pomiń kreskę
                    else:
                        max_scale = 1
                        min_scale = 0.5
                        scale_factor = 0.85  # it is arbitrarily chosen value

                        label_text = item.full_name
                        label_width = float(len(label_text)) * 15
                        text_scale = element_width / label_width * scale_factor
                        text_scale = max(min(text_scale, max_scale), min_scale)

                        label_width *= text_scale

                        tabela_tw.append(
                            (
                                label_text,
                                _cell - element_width / 2 - label_width / 2,
                                z - 4,
                                wys_r * text_scale,
                            )
                        )
                    # Dodaj linie pionowa
                    tabela_lw.append((_cell - korekta, z, _cell - korekta, z - wys_r))
                    _cell -= element_width

        return drawing

    def get_tubes(self):
        tube_codename = 'packaging_filling_raw_tube-'
        tubes = defaultdict(int)
        for element in self.all_elements:
            codename = element.additional.get('main_codename', '')
            if tube_codename not in codename:
                continue
            diameter = codename.lstrip(tube_codename)
            tubes[diameter] += 1
        return tubes

    def get_beaver_bumpers(self):
        beaver_partial_name = 'packaging_filling_raw_beaverboard-bumper-'
        for element in self.all_elements:
            codename = element.additional.get('main_codename', '')
            if beaver_partial_name in codename:
                return element

    def get_bumper_dimensions(self, bumper):
        depth = (bumper.z_domain[1] - bumper.z_domain[0]) // 100
        height = (bumper.y_domain[1] - bumper.y_domain[0]) // 100
        return f'{int(depth)}x{int(height)}mm'

    def get_elements_by_level(
        self,
        excluded_element_types=('SPACER', 'BUBBLE', 'L'),
    ):
        output = {
            lvl: [
                e
                for e in self.all_elements
                if e.package_info
                and e.package_info['pack_id'] == self.pack_id
                and e.package_info.get('pack_level') == lvl
                and e.ELEM_TYPE not in excluded_element_types
            ]
            for lvl in range(0, self.no_levels)
        }

        return output

    def get_package_vertices(self):
        """
        zwrawca reprezentacje paczki w 3d
        :return:
        """
        l, d, h = self.dim_x, self.dim_y, self.dim_z  # length, depth, height netto
        points = {
            'p1': [0, 0, h],
            'p2': [0, 0, 0],
            'p3': [l, 0, 0],
            'p4': [l, 0, h],
            'p5': [0, d, h],
            'p6': [0, d, 0],
            'p7': [l, d, 0],
            'p8': [l, d, h],
        }
        return points

    def get_package_faces(self):
        """
        zwrawca reprezentacje paczki w 3d
        :return:
        """
        pts = self.get_package_vertices()
        faces = {
            'front': [pts['p1'], pts['p2'], pts['p3'], pts['p4']],
            'back': [pts['p5'], pts['p6'], pts['p7'], pts['p8']],
            'top': [pts['p1'], pts['p4'], pts['p8'], pts['p5']],
            'bottom': [pts['p2'], pts['p3'], pts['p7'], pts['p6']],
            'left': [pts['p1'], pts['p2'], pts['p6'], pts['p5']],
            'right': [pts['p4'], pts['p3'], pts['p7'], pts['p8']],
        }

        return faces

    def get_package_gfx(self, elements_to_draw=None):
        """
        package isometric view for manual
        :param elements_to_draw: element.name
        :return:
        """
        faces = self.get_package_faces()
        faces_picked = [faces['back'], faces['bottom'], faces['right']]
        polygons = []
        _pts_flat = []
        font_size = Ivy_Settings.tag_text_size_medium
        for face in faces_picked:
            _pts = []
            for pt in face:
                _pt = Ivy_Elements_iso.IvyElementsIso.get_pt_iso(pt, direction=1)
                _pts.append((_pt[0], _pt[1]))
                _pts_flat.append((_pt[0], _pt[1]))
            polygons.append(dict(points=_pts))
        centroid = Ivy_Elements_iso.IvyElementsIso.get_element_centroid(
            polygons[0]['points']
        )
        _tag_size = Ivy_Settings.tag_circle_radius_medium
        circle = [
            {
                'center': (centroid[0], centroid[1]),
                'radius': _tag_size,
                'stroke-width': 4.5,
                'fill': 'white',
                'fill-opacity': 1.0,
                'lock': ['radius'],
            }
        ]

        text = [
            {
                'text': '#' + str(self.pack_id),
                'insert': (centroid[0], centroid[1]),
                'alignment-baseline': 'middle',
                'font-size': font_size,
                'font-weight': 'bold',
                'lock': ['font-size'],
            }
        ]

        gfx = [
            {
                'group_data': {
                    'id': 'pack',  # Nazwa ID
                    'fill': 'white',
                    'fill-opacity': 0.0,
                    'stroke': 'black',
                    'stroke-width': 1.5,
                    'stroke-linejoin': 'round',
                },
                'polygon': polygons,
            },
            {
                'group_data': {
                    'id': 'pack_tag',  # Nazwa ID
                    'fill': 'white',
                    'fill-opacity': 0.0,
                    'stroke': 'black',
                    'stroke-width': 1,
                    'stroke-linejoin': 'round',
                },
                'circle': circle,
            },
            {
                'group_data': {
                    'id': 'pack_text',  # Nazwa ID
                    'text-anchor': 'middle',
                    'fill': 'black',
                    'stroke': 'white',
                    'stroke-width': 0,
                    'text_size': 55,
                    'font-family': 'Messina Sans',
                    'alignment-baseline': 'central',
                },
                'text': text,
            },
        ]

        return gfx

    def get_form_lines(self, lines_num, div, start, end, y_offset=0, line_trim=0):

        lines = []
        group = {
            'graphic': [
                {
                    'group_data': {
                        'id': 'lines_form',
                    },
                    'line': lines,
                }
            ]
        }

        for line in range(lines_num):
            line = {
                'start': (start[0] + line_trim, start[1] + div * line + y_offset),
                'end': (end[0] - line_trim, end[1] + div * line + y_offset),
                'stroke': '#D3D3D3',
                'stroke-width': 1.0,
            }
            lines.append(line)
        return group

    def get_label_data(self, paczka='1/1', user_name='', address='', pack_num=''):
        """Zwraca slownik z danymi paczki do eksportu labelki logistyki na paczce."""
        # Ustawienia
        page_size = Ivy_Settings.box_label_size
        _insert = Ivy_Settings.text_insert
        add_ins = Ivy_Settings.text_address_insert

        logo_tylko = [Ivy_export_shapes.logo_poly]
        logo_tylko[0]['graphic'][0]['group_data']['fill'] = 'white'
        form_y = Ivy_Settings.text_leading
        f_off = 30
        div = Ivy_Settings.cells_div
        text_group = self.get_package_label_text(
            user_name=user_name, address=address, pack_num=pack_num
        )
        text_const = self.get_package_label_const_text()
        svg_name = str(self.id_production) + ' ' + str(paczka)
        lines = {
            'graphic': [
                {
                    'group_data': {'id': 'lines', 'stroke': 'black'},
                    'line': [
                        {
                            'start': (f_off, div[0]),
                            'end': (page_size[0] - f_off, div[0]),
                        },
                        {
                            'start': (f_off, div[1]),
                            'end': (page_size[0] - f_off, div[1]),
                        },
                        {
                            'start': (f_off, div[2]),
                            'end': (page_size[0] - f_off, div[2]),
                        },
                    ],
                }
            ]
        }
        rect = {
            'graphic': [
                {
                    'group_data': {'id': 'rect', 'fill': 'black'},
                    'rect': [
                        {
                            'insert': (f_off, f_off),
                            'size': (page_size[0] - 2 * f_off, div[0] - f_off),
                        }
                    ],
                }
            ]
        }

        form_lines_name = self.get_form_lines(
            2,
            form_y,
            start=(f_off, _insert[1]),
            end=(page_size[0] - f_off, _insert[1]),
            y_offset=5,
            line_trim=75,
        )
        form_lines_adr = self.get_form_lines(
            9,
            form_y,
            start=(f_off, add_ins[1]),
            end=(page_size[0] - f_off, add_ins[1]),
            y_offset=5,
            line_trim=75,
        )

        base_svg = self.get_label_svg_base_data(
            svg_name, page_size, page_num=0, frame_offset=f_off
        )
        logo_data = Ivy_export_SVG.ExportSVG.get_drawing_scaled_on_page(
            logo_tylko,
            insert_x=0.16,
            insert_y=0.052,
            page_size=page_size,
            scale=0.85,
            align='center',
            scale_by='page',
            flip=False,
        )

        base_svg[0]['graphic'] += rect['graphic']
        base_svg[0]['graphic'] += text_group['graphic']
        base_svg[0]['graphic'] += text_const['graphic']
        base_svg[0]['graphic'] += lines['graphic']
        base_svg[0]['graphic'] += form_lines_name['graphic']
        base_svg[0]['graphic'] += form_lines_adr['graphic']
        base_svg[0]['graphic'] += logo_data[0]['graphic']

        drawing = base_svg[0]
        return drawing

    def get_package_label_text(self, user_name=None, address=None, pack_num=None):
        """
        generuje text do wstawienia na labelki paczek
        :param user_name:
        :param address:
        :param pack_num:
        :return:
        """
        user_name = '-' if user_name is None else user_name
        address = ['-'] if address is None else address
        pack_num = '-' if pack_num is None else pack_num

        _text_lead = Ivy_Settings.text_leading  # dystans w y tekstu
        _insert = Ivy_Settings.text_insert
        text_title_size = Ivy_Settings.text_lab_title_size
        id_ins = Ivy_Settings.id_insert
        box_ins = Ivy_Settings.text_box_insert
        add_ins = Ivy_Settings.text_address_insert
        text_reg = Ivy_Settings.text_lab_regular_size

        _size_user = text_title_size * 0.6 if len(user_name) > 25 else text_title_size
        texts = []
        _ins_y = 0
        for i, t in enumerate([user_name]):
            _ins_y = _insert[1] + i * _text_lead
            texts.append(
                dict(
                    text=t,
                    insert=(_insert[0], _ins_y),
                    text_size=_size_user,
                    fill='black',
                )
            )

        _y = 0
        # check lenght of each text - if longer than treshold, split it and flatten list
        adr = address
        _size = text_reg * 0.6 if max([len(x) for x in adr]) > 25 else text_reg
        for i, a in enumerate(adr):
            _y = _text_lead * i
            texts.append(
                dict(
                    text=a,
                    insert=(_insert[0], add_ins[1] + _y),
                    text_size=_size,
                    fill='black',
                )
            )

        text_id = dict(
            text=self.id_production,
            text_size=text_reg * 3,
            insert=(_insert[0] + 135, id_ins[1]),
            fill='black',
        )
        texts.append(text_id)
        text_pack_num = dict(
            text=pack_num,
            text_size=text_reg * 1.25,
            insert=(box_ins[0] - _insert[0], box_ins[1]),
            fill='white',
        )
        text_box = self.get_label_texts_group([text_pack_num], fill='white')

        text_group = self.get_label_texts_group(texts)
        text_group['graphic'] += text_box['graphic']
        return text_group

    def get_label_texts_group(self, text_dict_list, fill='black'):
        text_list = []
        texts_drawing_data = {
            'graphic': [
                {
                    'group_data': {
                        'id': 'texts',
                        'text-anchor': 'start',
                        'stroke': 'white',
                        'stroke-width': 0,
                        'font-family': 'Messina Sans',
                        'font-weight': 'bold',
                    },
                    'text': text_list,
                }
            ]
        }
        for text_dict in text_dict_list:
            _text = {
                'text': text_dict['text'],
                'insert': text_dict['insert'],
                'text_size': text_dict['text_size'],
                'fill': fill,
            }
            text_list.append(_text)
        return texts_drawing_data

    def get_label_svg_base_data(
        self,
        file_name,
        size,
        page_num=0,
        page_size=(2970, 2100),
        bound_rect=True,
        frame_offset=0,
    ):
        # zwraca baze rys svg glownego do umieszczania wewnatrz mniejszych rysunkow
        page_border = {
            'rect': [
                {
                    'insert': (frame_offset, frame_offset),
                    'size': (size[0] - 2 * frame_offset, size[1] - 2 * frame_offset),
                    'stroke': 'black',
                    'fill': 'white',
                    'fill-opacity': 1.0,
                }
            ]
        }
        page_rect = {
            'rect': [
                {
                    'insert': (0, 0),
                    'size': (size[0], size[1]),
                    'fill': 'white',
                    'fill-opacity': 1.0,
                }
            ]
        }
        page_rect = page_rect if bound_rect else []
        base = [
            {
                'drawing': {'file_name': file_name, 'size': size},
                'graphic': [page_rect, page_border],
            }
        ]
        return base

    def get_package_label_const_text(self):
        """
        text staly na labelce, 'name:', 'address:' etc
        :return:
        """
        y_off = Ivy_Settings.const_y_offset
        _insert = Ivy_Settings.text_insert
        id_ins = Ivy_Settings.id_insert
        add_ins = Ivy_Settings.text_address_insert
        text_reg = Ivy_Settings.text_lab_regular_size * 0.7
        x_off = y_off
        name = dict(
            text='name:',
            text_size=text_reg,
            insert=(_insert[0] - old_div(x_off, 2), _insert[1] - y_off),
            fill='black',
        )
        add = dict(
            text='address:',
            text_size=text_reg,
            insert=(_insert[0] - old_div(x_off, 2), add_ins[1] - y_off),
            fill='black',
        )
        id = dict(
            text='shelf id:',
            text_size=text_reg,
            insert=(_insert[0] - old_div(x_off, 2), id_ins[1] - y_off),
            fill='black',
        )

        texts = [name, add, id]
        text_group = self.get_label_texts_group(texts, fill='black')  # fill='#A9A9A9'
        return text_group
