import logging
import typing

from collections import defaultdict
from typing import Optional

from django.conf import settings
from django.contrib.auth.models import User
from django.db import transaction
from django.db.models import QuerySet
from django.utils import timezone

from requests import HTTPError

from complaints.services.statuses import abort_reproduction_product
from custom.enums import ShelfType
from logger.choices import Source
from logger.models import Log
from mailing.templates import QualityControlNeededInfo
from orders.enums import OrderStatus
from producers.choices import ProductStatus
from producers.internal_api.events import (
    ProductAbortedEvent,
    ProductManufactorChangedEvent,
    ProductMovedFromLogisticByStatusOrderEvent,
    ProductStatusChangedEvent,
    ProductUpdatedSerializationEvent,
)
from producers.production_system_utils.client import PSProduct

logger = logging.getLogger('producers')

if typing.TYPE_CHECKING:
    from producers.models import Product


class PSConnectionError(HTTPError):
    """Custom exception for Production System connection errors."""


def _add_log(product: 'Product', owner: User, action: str, data: dict):
    Log.objects.create(
        user=owner,
        logger_source=Source.LOGGER_ADMIN,
        action=action,
        model=product._meta.label,
        model_id=product.pk,
        data=data,
    )


class ProductSerializationUpdater:
    def __init__(self, product: 'Product', is_retry=False):
        self.product = product
        self.is_retry = is_retry

    def change_manufactor(self, manufactor_new, owner):
        from producers.models import Manufactor

        manufactor_previous = self.product.manufactor
        self.product.manufactor = Manufactor.objects.filter(id=manufactor_new).first()
        self.product.save(update_fields=['manufactor'])
        data = {
            'manufactor_new': manufactor_new,
            'manufactor_previous': manufactor_previous.id,
        }
        _add_log(self.product, owner, action='change_manufactor', data=data)
        ProductManufactorChangedEvent(self.product)

    def _test_product_serialization(
        self,
        manufacturer_id=None,
        ppv: Optional[int] = None,
    ):
        """Backwards compatible function for updating serialization"""
        if not manufacturer_id:
            manufacturer_id = self.product.manufactor_id or -1

        with self.product._get_ps_client_context() as client:
            jetty = self.product.order_item_serialized
            if ppv is not None:
                jetty['physical_product_version'] = ppv
            ps_serialization = client.product_serialize(
                product=PSProduct(
                    gallery_json=jetty,
                    gallery_id=self.product.order_item.order_item.pk,
                    product_id=self.product.pk,
                    furniture_type=self.product.cached_product_type,
                    order_id=self.product.order_id,
                    manufacturer_id=manufacturer_id,
                    **self._complaint_serialization_kwargs,
                ),
                save=False,
            )
        if not ps_serialization:
            raise ValueError('Empty data from serialization')

        return ps_serialization

    @property
    def _complaint_serialization_kwargs(self):
        """Optional serialization keyword arguments when product is a complaint."""
        possible_complaint = self.product.reproduction_origin_complaint
        complaint_kwargs = {}
        if possible_complaint:
            base_product = (
                self.product if possible_complaint.has_deprecated_elements else None
            )
            complaint_kwargs['complaint_id'] = possible_complaint.id
            complaint_kwargs['complaint_product_id'] = self.product.copy_of_id
            elements_and_fittings = (
                possible_complaint.element_for_reproduction_with_full_names(
                    product=base_product,
                )
            )
            complaint_kwargs['complaint_elements'] = elements_and_fittings[0]
            complaint_kwargs['complaint_fittings'] = elements_and_fittings[1]
        return complaint_kwargs

    def gen_product_serialization(
        self,
        manufacturer_id=None,
    ):
        """Backwards compatible function for creating serialization."""
        if not manufacturer_id:
            manufacturer_id = self.product.manufactor_id or -1

        suppress_errors = not self.is_retry
        with self.product._get_ps_client_context(
            suppress_errors=suppress_errors
        ) as ps_client:
            try:
                ps_serialization = ps_client.product_serialize(
                    product=PSProduct(
                        gallery_json=self.product.order_item_serialized,
                        gallery_id=self.product.order_item.order_item.pk,
                        product_id=self.product.pk,
                        furniture_type=self.product.cached_product_type,
                        order_id=self.product.order_id,
                        manufacturer_id=manufacturer_id,
                        **self._complaint_serialization_kwargs,
                    ),
                    save=True,
                )
            except HTTPError as e:
                if e.response.status_code >= 500:
                    raise PSConnectionError from e

        if suppress_errors and not ps_serialization['item']:
            from producers.tasks import retry_update_product_serialization

            retry_update_product_serialization.apply_async(
                args=[self.product.pk],
                countdown=60,
            )

        if not ps_serialization:
            raise ValueError('Empty data from serialization')

        return ps_serialization

    def gen_product_serialization_after_ppv_update(self):
        manufacturer_id = -1
        self.product.order_item_serialized = self.product.get_gallery_item_serialized()
        self.product.save(update_fields=['order_item_serialized'])
        with self.product._get_ps_client_context(suppress_errors=True) as ps_client:
            ps_serialization = ps_client.product_serialize(
                product=PSProduct(
                    gallery_json=self.product.order_item_serialized,
                    gallery_id=self.product.order_item.order_item.pk,
                    product_id=self.product.pk,
                    furniture_type=self.product.cached_product_type,
                    order_id=self.product.order_id,
                    manufacturer_id=manufacturer_id,
                ),
                save=True,
            )
        if not ps_serialization:
            raise ValueError('Empty data from serialization')
        self.product.details.cached_serialization = ps_serialization
        self.product.details.save(update_fields=['cached_serialization'])

    def update_product_serialization(self):
        """
        Update product serialization.

        Connects to Production System and updates the product in it as well.
        """
        if self.product.cached_product_type not in {
            self.product.JETTY,
            self.product.WATTY,
            self.product.SOTTY,
        }:
            return

        # update methods use cached jetty serialization, so it needs to be updated
        self.product.order_item_serialized = self.product.get_gallery_item_serialized(
            order=self.product.order
        )
        self.product.details.cached_serialization = self.gen_product_serialization()
        self.product.details.save()
        self.update_cached_features()
        self.update_batch_actions_needed()
        self.product.save()
        ProductUpdatedSerializationEvent(self.product)
        return self.product.details.cached_serialization

    def update_batch_actions_needed(self):
        """Batch actions needed status changes when any of its Products are changed."""
        if not self.product.batch:
            return
        actions_flow = self.product.batch.actions_needed_flow
        if self.product.details.cached_serialization['errors']:
            actions_flow.process_product_serialization_recalculated_with_errors()
        else:
            actions_flow.process_product_serialization_recalculated_correct()

    def update_cached_features(self):
        """
        Function that updates them all
        And in the darkness binds them!
        In the land of items in production
        where cached features lie
        """
        if self.product.cached_product_type not in self.product.PS_SERIALIZED:
            return
        self.product.has_errors = self.product.item_has_errors()
        self.product.is_extended = self.product.item_is_extended()
        self.product.cover_only = self.product.order_item.cover_only
        gallery_item = self.product.order_item.order_item
        self.product.cached_depth = gallery_item.depth
        self.product.cached_width = gallery_item.width
        self.product.cached_height = gallery_item.height
        self.product.cached_dna_style = gallery_item.pattern
        try:
            self.product.cached_shelf_type = ShelfType(
                gallery_item.shelf_type,
            ).production_code
        except ValueError:
            self.product.cached_shelf_type = None
        item_features = gallery_item.get_item_features(
            self.product.order.order_type,
        )
        self.product.cached_has_top_or_bottom_storage = 'storage' in item_features
        self.product.has_plinth = (
            False
            if self.product.cached_product_type != 'jetty'
            else gallery_item.has_plinth
        )
        self.product.has_doors = 'doors' in item_features
        self.product.has_plus_feature = 'plus_feature' in item_features
        self.product.has_drawers = 'drawers' in item_features
        self.product.cached_area = self.product.get_m2()
        self.product.cached_banding_length = self.product.get_banding_length()
        self.product.cached_material = gallery_item.material
        if self.product.cached_product_type == self.product.SOTTY:
            self.product.cached_materials = gallery_item.materials
        else:
            self.product.cached_material_color = (
                gallery_item.get_material_description()['colour']['html']
            )
            self.product.cached_second_color = gallery_item.get_material_description()[
                'colour'
            ]['html_second_color']
            self.product.cached_configurator_type = (
                self.product.get_item_configurator_type()
            )
        self.product.cached_physical_product_version = (
            self.product.get_item_physical_product_version()
        )
        self.product.is_sidebohr = self.product.item_is_sidebohr()
        self.product.is_desk = 'desk' in item_features
        self.product.has_double_lamello = self.product.get_has_double_lamello()
        self.product.has_left_door_with_handle = (
            self.product.get_has_left_door_with_handle()
        )
        self.product.has_back_top_b = self.product.get_has_back_top_b()
        self.product.has_wall_ee = self.product.get_has_wall_ee()
        self.product.vertical_heights = self.product.get_vertical_heights()
        self.product.has_lighting = 'lighting' in item_features
        self.product.has_topology = (
            'depth_topology' in item_features or 'height_topology' in item_features
        )
        self.product.save()
        ProductUpdatedSerializationEvent(self.product)


def get_owner_or_default_user(owner):
    return owner if owner is not None else User.objects.get(pk=1)


class ProductStatusUpdater:
    def __init__(self, product: 'producers.Product'):  # noqa: F821
        self.product = product

    def check_status_change(self):
        # FIXME: Add init_status, that check is true almost for all TBS and Aborted done
        if (
            self.product.status != self.product.previous_status
            and self.product.previous_status != ProductStatus.QUALITY_BLOCKER
            and self.product.status
            in [ProductStatus.TO_BE_SHIPPED, ProductStatus.ABORTED_DONE]
        ):
            self.set_order_to_be_shipped()

    def set_order_to_be_shipped(self) -> None:
        order = self.product.order
        not_ready_to_be_shipped_products = order.product_set.exclude(
            status__in=[
                ProductStatus.SENT_TO_CUSTOMER,
                ProductStatus.DELIVERED_TO_CUSTOMER,
                ProductStatus.TO_BE_SHIPPED,
                ProductStatus.ABORTED,
                ProductStatus.ABORTED_DONE,
            ]
        )
        self.set_logistic_order_to_be_shipped()
        if (
            not not_ready_to_be_shipped_products.exists()
            and order.status != OrderStatus.CANCELLED
        ):
            order.change_status(OrderStatus.TO_BE_SHIPPED)

    def set_logistic_order_to_be_shipped(self) -> None:
        from orders.internal_api.events import (
            LogisticOrderToBeShippedStatusChangedEvent,
        )

        if self.product.logistic_order:
            ProductStatusChangedEvent(self.product)
            # edd orders should be earlier in tbs status
            if not self.product.order.is_estimated_delivery_date:
                # run even if logistic order is already in this status
                LogisticOrderToBeShippedStatusChangedEvent(
                    self.product.order, self.product.logistic_order
                )

    def change_status_to_be_shipped_after_release(self):
        user = User.objects.get_by_natural_key(settings.CELERY_SUPERUSER_USERNAME)
        # prevent if the manufacturer wants to release package for second time
        # on quality_control status
        if self.product.status == ProductStatus.QUALITY_CONTROL:
            self.notify_about_quality_control_needed()
        else:
            self.change_status(
                status_to_change=ProductStatus.TO_BE_SHIPPED,
                owner=user,
            )

    def can_change_status_to_be_shipped(self):
        release_package_count = self.product.manufacturerreleasedpackage_set.count()
        packaging_quantity = self.product.get_packaging_quantity()
        allowed_statuses = {
            ProductStatus.NEW,
            ProductStatus.ASSIGNED_TO_PRODUCTION,
            ProductStatus.IN_PRODUCTION,
            ProductStatus.INTERNAL_USAGE,
        }
        return (
            self.product.status in allowed_statuses
            and release_package_count == packaging_quantity
        )

    def change_status(
        self, status_to_change, owner=None, should_callback_logistic=True
    ):
        from orders.internal_api.events import (
            LogisticOrderToBeShippedStatusRollbackEvent,
        )
        from orders.services.order_abort import get_note_message

        self.product.previous_status = self.product.status
        self.product.status = status_to_change

        if (
            status_to_change == ProductStatus.TO_BE_SHIPPED
            and self.product.previous_status
            in [ProductStatus.ABORTED, ProductStatus.ABORTED_DONE]
        ):
            self.product.status = ProductStatus.ABORTED_DONE
            status_to_change = ProductStatus.ABORTED_DONE

        if (
            status_to_change == ProductStatus.TO_BE_SHIPPED
            and self.product.quality_control_needed
            and self.product.previous_status != ProductStatus.QUALITY_CONTROL
        ):
            self.notify_about_quality_control_needed()
            self.product.status = ProductStatus.QUALITY_CONTROL
            status_to_change = ProductStatus.QUALITY_CONTROL
        if status_to_change == ProductStatus.QUALITY_BLOCKER:
            LogisticOrderToBeShippedStatusRollbackEvent(
                self.product.order, self.product.logistic_order
            )
        if (
            status_to_change == ProductStatus.ABORTED
            and self.product.previous_status != ProductStatus.ABORTED
        ):
            abort_reproduction_product(reproduction_product=self.product)

            note = get_note_message(
                self.product.order.status, self.product.previous_status
            )
            self.product.notes = self.product.notes
            if self.product.notes:
                self.product.notes += '\n'
            self.product.notes += note
            self.product.save(update_fields=['notes'])

        history_entry = self._add_product_status_history(owner, status_to_change)
        self.product.save(update_fields=['status', 'previous_status'])
        if should_callback_logistic:
            if (
                status_to_change in [ProductStatus.ABORTED, ProductStatus.ABORTED_DONE]
                and self.product.logistic_order
            ):
                ProductAbortedEvent(self.product)
            else:
                ProductStatusChangedEvent(self.product)

        log_data = {
            'new_status': self.product.status,
            'previous_status': self.product.previous_status,
            'history_entry': history_entry.pk,
        }
        _add_log(self.product, owner, action='change_status', data=log_data)

    def notify_about_quality_control_needed(self):
        data_html = {
            'product_id': self.product.id,
            'manufactor__name': self.product.manufactor.name,
            'get_priority_display': self.product.get_priority_display(),
        }
        for name, email in settings.QUALITY_CONTROL_EMAILS:
            mail = QualityControlNeededInfo(
                email,
                data_html=data_html,
                topic=f'Quality Control Needed for Product {self.product.id}',
            )
            mail.send()

    def _add_product_status_history(self, owner, status_to_change):
        from producers.models import ProductStatusHistory

        history_entry = ProductStatusHistory.objects.create(
            product=self.product,
            owner=get_owner_or_default_user(owner),
            changed_at=timezone.now(),
            status=status_to_change,
            previous_status=self.product.previous_status,
        )
        return history_entry


class ProductStatusUpdaterWithLogisticSplit:
    def __init__(
        self,
        products: QuerySet['Product'],
        target_status: ProductStatus,
    ) -> None:
        self.products = products
        self.target_status = target_status

    @transaction.atomic
    def update_status_and_split_logistic_orders(self, user: 'User') -> int:
        from orders.internal_api.events import OrderFullRefreshEvent

        products_status_changed_count = 0

        products_by_order = defaultdict(list)

        for product in self.products:
            product.status_updater.change_status(
                self.target_status,
                user,
                should_callback_logistic=False,
            )
            products_by_order[product.order].append(product)
            products_status_changed_count += 1

        for order, products in products_by_order.items():
            self.bulk_update_logistic_order(products)
            OrderFullRefreshEvent(order)

        return products_status_changed_count

    def _group_products_by_logistic_and_status(self, products: list['Product']) -> dict:
        products_by_logistic_order_and_status = defaultdict(lambda: defaultdict(list))
        for product in products:
            if product.logistic_order:
                products_by_logistic_order_and_status[product.logistic_order][
                    product.status
                ].append(product.id)
        return products_by_logistic_order_and_status

    def bulk_update_logistic_order(self, products: list['Product']) -> None:
        from producers.models import Product

        products_by_logistic_order_and_status = (
            self._group_products_by_logistic_and_status(products)
        )

        for (
            logistic_order_id,
            status_groups,
        ) in products_by_logistic_order_and_status.items():
            for status, products_ids in status_groups.items():
                event = ProductMovedFromLogisticByStatusOrderEvent(
                    logistic_order_id=logistic_order_id,
                    products_ids=products_ids,
                    status=status,
                )
                Product.objects.filter(id__in=products_ids).update(
                    logistic_order=event.target_logistic_order_id
                )
