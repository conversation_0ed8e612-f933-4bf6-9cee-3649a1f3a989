import datetime

from decimal import Decimal
from unittest import mock
from unittest.mock import patch

from django.http import HttpRequest
from django.test import override_settings
from django.utils import timezone

import pytest

from freezegun import freeze_time

from customer_service.correction_request_strategies import (
    CorrectionRequestFreeReturnStrategy,
)
from customer_service.enums import (
    CSCorrectionRequestStatus,
    CSCorrectionRequestType,
)
from customer_service.models import CSCorrectionRequest
from free_returns.enums import (
    FreeReturnStatusChoices,
    FreeReturnTransportChoices,
)
from free_returns.tasks import (
    CorrectionRequestFreeReturnHandler,
    check_for_finished_returns,
    check_for_tnt_orders,
)
from free_returns.tests.factories import FreeReturnFactory
from invoice.choices import (
    InvoiceItemType,
    InvoiceStatus,
)
from invoice.models import Invoice
from invoice.tests.factories import InvoiceSequenceFactory
from orders.models import OrderItem
from orders.tests.factories import OrderItemFactory

DATE_2023_02_06 = datetime.datetime(2023, 2, 6, 11, tzinfo=datetime.UTC)


@pytest.fixture
def invoice_sequence():
    return InvoiceSequenceFactory(
        invoice_type=4, numeration_type=0, country__name='poland'
    )


@pytest.fixture
def invoice(order_with_delivered_logistic_order, invoice_factory):
    return invoice_factory(
        status=InvoiceStatus.ENABLED,
        pretty_id='test/DE',
        order=order_with_delivered_logistic_order,
    )


@pytest.fixture
def mock_request(admin_user):
    mock_request = HttpRequest()
    mock_request.user = admin_user
    return mock_request


def get_corrected_invoice_items_gross_price(invoice_correction):
    return invoice_correction.invoice_items.filter(
        item_type=InvoiceItemType.ITEM
    ).values_list('gross_price', flat=True)


def get_corrected_invoice_service_gross_price(invoice_correction):
    return invoice_correction.invoice_items.filter(
        item_type__in=[
            InvoiceItemType.ASSEMBLY,
            InvoiceItemType.DELIVERY,
            InvoiceItemType.SERVICE,
        ]
    ).values_list('gross_price', flat=True)


@pytest.mark.django_db
@pytest.mark.nbp
class TestCheckForFinishedReturns:
    @override_settings(CELERY_SUPERUSER_USERNAME='admin')
    def test_invoice_neutralize(
        self, mocker, free_return_all_items, admin_user, invoice
    ):
        mocker.patch('invoice.models.Invoice.create_pdf')
        free_return_all_items.finished_at = timezone.now()
        free_return_all_items.save()
        check_for_finished_returns()
        assert CSCorrectionRequest.objects.count() == 1
        assert (
            CSCorrectionRequest.objects.first().type_cs
            == CSCorrectionRequestType.TYPE_NEUTRALIZATION
        )

    @override_settings(CELERY_SUPERUSER_USERNAME='admin')
    def test_invoice_partial_return(
        self,
        mocker,
        free_return_only_some_items,
        admin_user,
        invoice,
    ):
        mocker.patch('invoice.models.Invoice.create_pdf')
        free_return_only_some_items.finished_at = timezone.now()
        free_return_only_some_items.save()
        check_for_finished_returns()
        assert CSCorrectionRequest.objects.count() == 1
        assert (
            CSCorrectionRequest.objects.first().type_cs
            == CSCorrectionRequestType.TYPE_FREE_RETURN
        )

    @override_settings(CELERY_SUPERUSER_USERNAME='admin')
    def test_double_correction_partial_return_and_then_neutralize(
        self,
        mocker,
        free_return_only_some_items,
        admin_user,
        invoice,
        free_return_factory,
    ):
        # 1. Order with 4 items
        # 2. Create free return for 1 item
        # 3. Check_for_finished_returns creates correction request(remove invoice items)
        # 4. Accept CSCorrectionRequest and set free return to finished
        # 5. Create another free return for 3 items
        # 6. Check_for_finished_returns creates correction request (neutralize invoice)
        mocker.patch('invoice.models.Invoice.create_pdf')
        free_return_only_some_items.finished_at = timezone.now()
        free_return_only_some_items.save()
        check_for_finished_returns()
        assert CSCorrectionRequest.objects.count() == 1
        assert (
            CSCorrectionRequest.objects.first().type_cs
            == CSCorrectionRequestType.TYPE_FREE_RETURN
        )
        CSCorrectionRequest.objects.update(
            status=CSCorrectionRequestStatus.STATUS_ACCEPTED
        )
        free_return_only_some_items.status = FreeReturnStatusChoices.ACCEPTED_CORRECTION
        free_return_only_some_items.save()
        second_free_return = free_return_factory(
            notification_date=timezone.now(),
            transport_method=FreeReturnTransportChoices.TNT,
            status=FreeReturnStatusChoices.DELIVERED,
            finished_at=timezone.now(),
        )
        OrderItem.objects.filter(free_return__isnull=True).update(
            free_return=second_free_return
        )
        check_for_finished_returns()
        assert CSCorrectionRequest.objects.count() == 2
        assert (
            CSCorrectionRequest.objects.last().type_cs
            == CSCorrectionRequestType.TYPE_NEUTRALIZATION
        )

    @override_settings(CELERY_SUPERUSER_USERNAME='admin')
    def test_request_not_created_missing_invoice(
        self, free_return_all_items, admin_user
    ):
        free_return_all_items.finished_at = timezone.now()
        free_return_all_items.save()
        check_for_finished_returns()
        assert CSCorrectionRequest.objects.count() == 0

    @override_settings(CELERY_SUPERUSER_USERNAME='admin')
    def test_skip_finished_returns(
        self,
        mocker,
        free_return_all_items,
        admin_user,
        invoice,
        invoice_factory,
    ):
        mocker.patch('invoice.models.Invoice.create_pdf')
        # 1. Create free return
        # 2. Check_for_finished_returns creates CSCorrectionRequest
        # 3. Simulate accept CSCorrectionRequest and set free return to finished
        # 4. No more CSCorrectionRequest should be created
        free_return_all_items.finished_at = timezone.now()
        free_return_all_items.save()

        check_for_finished_returns()
        assert CSCorrectionRequest.objects.count() == 1
        invoice_correction = invoice_factory(
            status=InvoiceStatus.CORRECTING,
            pretty_id='test/DE',
            order=invoice.order,
            corrected_invoice=invoice,
        )

        correction_request = CSCorrectionRequest.objects.first()
        correction_request.status = CSCorrectionRequestStatus.STATUS_ACCEPTED
        correction_request.save()
        free_return_all_items.status = FreeReturnStatusChoices.ACCEPTED_CORRECTION
        free_return_all_items.save()
        for item in invoice.invoice_items.all():
            item.invoice_id = invoice_correction.id
            item.corrected_invoice_item_id = item.id
            item.save()

        check_for_finished_returns()
        assert CSCorrectionRequest.objects.count() == 1

    @override_settings(CELERY_SUPERUSER_USERNAME='admin')
    @patch(
        'custom.models.models.ExchangeRate.get_safe_exchange',
        return_value={'EUR': 4.6909},
    )
    @patch('invoice.internal_api.events.InvoiceGeneratedEvent.execute')
    def test_partial_return__corrected_invoice_with_assembly(
        self,
        mocked_invoice_generated_event_execute,
        _,
        mocker,
        order_with_assembly_delivered_logistic_order,
        free_return_half_items_and_with_assembly,
        admin_user,
        invoice_sequence,
    ):
        # 1. Order with 2 items with assembly service
        # 2. Create free return for 1 item
        # 3. Check_for_finished_returns creates correction request(free return type)
        # 4. Accept correction request
        # 5. Correction invoice is expected to have 0 for returned item
        mocker.patch('invoice.models.Invoice.create_pdf')
        order = order_with_assembly_delivered_logistic_order
        invoice = order.invoice_set.order_by('id').last()
        invoice.save()  # create invoice items
        check_for_finished_returns()

        assert CSCorrectionRequest.objects.count() == 1
        assert (
            CSCorrectionRequest.objects.first().type_cs
            == CSCorrectionRequestType.TYPE_FREE_RETURN
        )
        correction_request = CSCorrectionRequest.objects.first()
        CorrectionRequestFreeReturnStrategy(correction_request).accept(user=admin_user)
        invoice_correction = Invoice.objects.get(corrected_invoice_id=invoice.id)
        invoice_correction_assembly_prices = get_corrected_invoice_service_gross_price(
            invoice_correction
        )
        invoice_correction_items_prices = get_corrected_invoice_items_gross_price(
            invoice_correction
        )

        expected_assebmly_price = {Decimal('150.00'), Decimal('90.00')}
        expected_items_prices = {Decimal('666.00'), Decimal('0.00')}

        assert set(invoice_correction_assembly_prices) == expected_assebmly_price
        assert set(invoice_correction_items_prices) == expected_items_prices

    @override_settings(CELERY_SUPERUSER_USERNAME='admin')
    @patch(
        'custom.models.models.ExchangeRate.get_safe_exchange',
        return_value={'EUR': 4.6909},
    )
    @patch('invoice.internal_api.events.InvoiceGeneratedEvent.execute')
    def test_return_all_items__corrected_invoice(
        self,
        mocked_invoice_generated_event_execute,
        _,
        mocker,
        order_with_assembly_delivered_logistic_order,
        free_return_all_items_with_assembly,
        admin_user,
        invoice_sequence,
    ):
        # 1. Order with 2 items with assembly service
        # 2. Create free return for 2 items
        # 3. Check_for_finished_returns creates -> correction request(free return type)
        # 4. Accept correction request
        # 5. Correction invoice is expected to have original assembly price and 0 for
        # returned items
        mocker.patch('invoice.models.Invoice.create_pdf')
        order = order_with_assembly_delivered_logistic_order
        invoice = order.invoice_set.order_by('id').last()

        invoice.save()  # create invoice items
        check_for_finished_returns()
        assert CSCorrectionRequest.objects.count() == 1
        assert (
            CSCorrectionRequest.objects.first().type_cs
            == CSCorrectionRequestType.TYPE_FREE_RETURN
        )
        correction_request = CSCorrectionRequest.objects.first()
        CorrectionRequestFreeReturnStrategy(correction_request).accept(user=admin_user)
        invoice_correction = Invoice.objects.get(corrected_invoice_id=invoice.id)
        invoice_correction_assembly_prices = get_corrected_invoice_service_gross_price(
            invoice_correction
        )
        invoice_correction_items_prices = get_corrected_invoice_items_gross_price(
            invoice_correction
        )

        expected_assembly_prices = {Decimal('90.00'), Decimal('150.0')}
        expected_items_prices = {Decimal('0.00'), Decimal('0.00')}
        assert set(invoice_correction_assembly_prices) == expected_assembly_prices
        assert set(invoice_correction_items_prices) == expected_items_prices

    @override_settings(CELERY_SUPERUSER_USERNAME='admin')
    @patch(
        'custom.models.models.ExchangeRate.get_safe_exchange',
        return_value={'EUR': 4.6909},
    )
    @patch('invoice.internal_api.events.InvoiceGeneratedEvent.execute')
    def test_order_with_assembly_invoice_without_assembly_item(
        self,
        mocked_invoice_generated_event_execute,
        _,
        mocker,
        order_with_assembly_delivered_logistic_order,
        free_return_all_items_with_assembly,
        admin_user,
        invoice_sequence,
    ):
        mocker.patch('invoice.models.Invoice.create_pdf')
        order = order_with_assembly_delivered_logistic_order
        invoice = order.invoice_set.order_by('id').last()
        invoice.save()  # create invoice items
        correction_handler = CorrectionRequestFreeReturnHandler(
            free_return_all_items_with_assembly,
            order.id,
            invoice,
            admin_user,
        )
        assert correction_handler.is_return_for_all_order_items

    @override_settings(CELERY_SUPERUSER_USERNAME='admin')
    @patch(
        'custom.models.models.ExchangeRate.get_safe_exchange',
        return_value={'EUR': 4.6909},
    )
    @patch('invoice.internal_api.events.InvoiceGeneratedEvent.execute')
    def test_return_all_items__corrected_invoice_set_material_items_to_0(
        self,
        mocked_invoice_generated_event_execute,
        _,
        order_with_assembly_delivered_logistic_order,
        free_return_all_items_with_assembly,
        admin_user,
        invoice_sequence,
    ):
        # 1. Order with 2 items with assembly service
        # 2. Create free return for 2 items
        # 3. Check_for_finished_returns creates -> correction request(neutralization)
        # 4. Accept correction request
        # 5. Correction invoice is expected to have original assembly price and 0 for
        # returned items

        order = order_with_assembly_delivered_logistic_order
        invoice = order.invoice_set.order_by('id').last()

        invoice.save()  # create invoice items
        check_for_finished_returns()
        assert CSCorrectionRequest.objects.count() == 1
        assert (
            CSCorrectionRequest.objects.first().type_cs
            == CSCorrectionRequestType.TYPE_FREE_RETURN
        )
        correction_request = CSCorrectionRequest.objects.first()
        CorrectionRequestFreeReturnStrategy(correction_request).accept(user=admin_user)
        invoice_correction = Invoice.objects.get(corrected_invoice_id=invoice.id)
        invoice_correction_assembly_prices = get_corrected_invoice_service_gross_price(
            invoice_correction
        )
        invoice_correction_items_prices = get_corrected_invoice_items_gross_price(
            invoice_correction
        )

        expected_items_prices = {Decimal('0.00'), Decimal('0.00')}
        expected_assembly_prices = {Decimal('150.00'), Decimal('90.00')}
        assert set(invoice_correction_assembly_prices) == expected_assembly_prices
        assert set(invoice_correction_items_prices) == expected_items_prices

    @override_settings(CELERY_SUPERUSER_USERNAME='admin')
    @patch(
        'custom.models.models.ExchangeRate.get_safe_exchange',
        return_value={'EUR': 4.6909},
    )
    @patch('invoice.internal_api.events.InvoiceGeneratedEvent.execute')
    def test_partial_return_no_assembly__corrected_invoice_without_assembly(
        self,
        mocked_invoice_generated_event_execute,
        _,
        order_with_delivered_logistic_order,
        free_return_half_items_no_assembly,
        admin_user,
        invoice_sequence,
    ):
        # 1. Order with 2 items with assembly service
        # 2. Create free return for 1 item and set free return assembly price
        # we want to refund 50% of assembly service for that item
        # 3. check_for_finished_returns creates correction request(free return type)
        # 4. accept correction request
        # 5. correction invoice is expected to have new assembly price and 0 for
        # returned item
        order = order_with_delivered_logistic_order
        invoice = order.invoice_set.order_by('id').last()
        invoice.save()  # create invoice items
        check_for_finished_returns()

        assert CSCorrectionRequest.objects.count() == 1
        assert (
            CSCorrectionRequest.objects.first().type_cs
            == CSCorrectionRequestType.TYPE_FREE_RETURN
        )
        correction_request = CSCorrectionRequest.objects.first()
        CorrectionRequestFreeReturnStrategy(correction_request).accept(user=admin_user)
        invoice_correction = Invoice.objects.get(corrected_invoice_id=invoice.id)
        invoice_correction_assembly_prices = get_corrected_invoice_service_gross_price(
            invoice_correction
        )
        invoice_correction_items_prices = get_corrected_invoice_items_gross_price(
            invoice_correction
        )

        expected_items_prices = {Decimal('666.00'), Decimal('0.00')}
        assert set(invoice_correction_assembly_prices) == set()
        assert set(invoice_correction_items_prices) == expected_items_prices

    def test_can_create_request_correction__return_false_when_0_total_net(
        self, invoice, admin_user, invoice_item_factory
    ):
        invoice_item_factory(
            invoice=invoice,
            net_value=0,
            item_type=InvoiceItemType.ITEM,
        )
        result = CorrectionRequestFreeReturnHandler.can_create_correction_request(
            invoice
        )
        assert result is False

    def test_can_create_request_correction__return_false_when_pending_corrections(
        self, cs_correction_request_factory, admin_user, invoice, invoice_item_factory
    ):
        cs_correction_request_factory(
            invoice=invoice,
            issuer=admin_user,
        )
        invoice_item_factory(
            invoice=invoice,
            net_value=0,
            item_type=InvoiceItemType.ITEM,
        )
        result = CorrectionRequestFreeReturnHandler.can_create_correction_request(
            invoice
        )
        assert result is False

    @override_settings(CELERY_SUPERUSER_USERNAME='admin')
    def test_create_correction_request__neutralization(
        self,
        mocker,
        free_return_all_items,
        invoice,
        admin_user,
    ):
        mocker.patch('invoice.models.Invoice.create_pdf')
        correction_handler = CorrectionRequestFreeReturnHandler(
            free_return_all_items, invoice.order_id, invoice, admin_user
        )
        correction_handler.create_correction()
        assert CSCorrectionRequest.objects.count() == 1
        assert (
            CSCorrectionRequest.objects.first().type_cs
            == CSCorrectionRequestType.TYPE_NEUTRALIZATION
        )

    @override_settings(CELERY_SUPERUSER_USERNAME='admin')
    def test_create_correction_request__free_return_type_when_two_free_returns(
        self,
        mocker,
        invoice,
        admin_user,
    ):
        """
        Order with 2 order items
        Create 2 free returns:
           FR1 - (order_item_1), FR2 - (order_item_2)
        Create correction request.
        We expect to have correction request for FR1, type FREE RETURN,
        not neutralization
        """
        mocker.patch('invoice.models.Invoice.create_pdf')
        first_free_return, second_free_return = FreeReturnFactory.create_batch(
            2,
            notification_date=timezone.now(),
            transport_method=FreeReturnTransportChoices.TNT,
            status=FreeReturnStatusChoices.DELIVERED,
            finished_at=timezone.now(),
        )
        OrderItemFactory(
            order=invoice.order,
            free_return=first_free_return,
        )
        OrderItemFactory(
            order=invoice.order,
            free_return=second_free_return,
        )
        correction_handler = CorrectionRequestFreeReturnHandler(
            first_free_return, invoice.order_id, invoice, admin_user
        )
        correction_handler.create_correction()
        assert CSCorrectionRequest.objects.count() == 1
        assert (
            CSCorrectionRequest.objects.first().type_cs
            == CSCorrectionRequestType.TYPE_FREE_RETURN
        )

    @override_settings(CELERY_SUPERUSER_USERNAME='admin')
    def test_create_correction_request__free_return_type(
        self,
        mocker,
        free_return_only_some_items,
        invoice,
        admin_user,
    ):
        mocker.patch('invoice.models.Invoice.create_pdf')
        correction_handler = CorrectionRequestFreeReturnHandler(
            free_return_only_some_items, invoice.order_id, invoice, admin_user
        )
        correction_handler.create_correction()
        assert CSCorrectionRequest.objects.count() == 1
        assert (
            CSCorrectionRequest.objects.first().type_cs
            == CSCorrectionRequestType.TYPE_FREE_RETURN
        )


@pytest.mark.django_db
class TestCheckForTNTOrders:
    @pytest.fixture
    def free_return_with_tracking_number(self, free_return_all_items):
        free_return_all_items.tracking_number = '4815162342'
        free_return_all_items.save()
        return free_return_all_items

    @mock.patch(
        'free_returns.tasks_utils.CheckTNTDeliveredDateByTrackingNumberAPIClient.get_delivery_date'
    )
    @freeze_time(DATE_2023_02_06)
    def test_full_scenario__finished_return(
        self, mocked_get_delivery_date, free_return_with_tracking_number, invoice
    ):
        mocked_get_delivery_date.return_value = timezone.now()
        free_return_with_tracking_number.tracking_number = '4815162342'
        free_return_with_tracking_number.save()
        check_for_tnt_orders()
        free_return_with_tracking_number.refresh_from_db()
        assert free_return_with_tracking_number.finished_at == DATE_2023_02_06
        free_return_with_tracking_number.refresh_from_db()
        assert free_return_with_tracking_number.finished_at == DATE_2023_02_06
        assert (
            free_return_with_tracking_number.status == FreeReturnStatusChoices.DELIVERED
        )

    @mock.patch(
        'free_returns.tasks_utils.CheckTNTDeliveredDateByTrackingNumberAPIClient.get_delivery_date',
        return_value=None,
    )
    def test_not_finished_return_status_changed_to_in_transport(
        self, mocked_get_delivery_date, free_return_with_tracking_number, invoice
    ):
        check_for_tnt_orders()
        free_return_with_tracking_number.refresh_from_db()
        assert free_return_with_tracking_number.finished_at is None
        assert (
            free_return_with_tracking_number.status
            == FreeReturnStatusChoices.IN_TRANSPORT
        )
